package dz.sonatrach.weblqs.mayaaback.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration du cache pour améliorer les performances
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        return new ConcurrentMapCacheManager(
            "dashboard-consolide",
            "list-unites",
            "autoconsommation",
            "gaz-torche",
            "indicateurs-performance",
            "utilisation-chaudieres",
            "evolution-causes",
            "arrets-data"
        );
    }
}
