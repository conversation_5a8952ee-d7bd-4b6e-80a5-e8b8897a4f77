package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.dto.DashboardConsolideResponse;
import dz.sonatrach.weblqs.mayaaback.service.DashboardService;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Contrôleur pour le dashboard consolidé
 */
@RestController
@RequestMapping("api/")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    /**
     * Récupère toutes les données consolidées pour le dashboard
     * @param pmois Période au format ddMMyyyy
     * @return Données consolidées du dashboard
     */
    @GetMapping("/dashboard-consolide/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<DashboardConsolideResponse> getDashboardConsolide(@PathVariable String pmois) {
        try {
            System.out.println("=== REQUETE DASHBOARD CONSOLIDE ===");
            System.out.println("Période demandée: " + pmois);
            
            LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
            DashboardConsolideResponse response = dashboardService.getDashboardConsolide(date);
            
            if (response == null || response.isEmpty()) {
                System.out.println("Aucune donnée trouvée pour " + pmois);
                return ResponseEntity.noContent().build();
            }
            
            System.out.println("Données consolidées retournées avec succès");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            System.err.println("Erreur dans le contrôleur dashboard pour " + pmois + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Test simple pour vérifier que l'endpoint fonctionne
     */
    @GetMapping("/dashboard-test")
    public ResponseEntity<String> testDashboard() {
        return ResponseEntity.ok("Dashboard endpoint is working!");
    }
}
