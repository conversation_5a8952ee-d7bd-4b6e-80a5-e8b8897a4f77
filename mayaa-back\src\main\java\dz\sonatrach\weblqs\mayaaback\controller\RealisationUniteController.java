package dz.sonatrach.weblqs.mayaaback.controller;

import dz.sonatrach.weblqs.mayaaback.model.RealisationUnite;
import dz.sonatrach.weblqs.mayaaback.repository.RealisationUniteRepository;
import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Contrôleur pour l'accès aux données de la vue REALISATION_UNITE.
 *
 * Endpoints principaux :
 *   GET /api/realisation-unite/{pmois} - Récupère les données de réalisation pour toutes les unités
 *   GET /api/realisation-unite/{pmois}/{unite} - Récupère les données pour une unité spécifique
 *
 * - pmois : période au format ddMMyyyy (ex: 01122024)
 * - unite : code de l'unité (String, ex: "5X2")
 *
 * Réponses :
 *   200 OK : données trouvées
 *   204 No Content : aucune donnée trouvée
 */
@RestController
@RequestMapping("api/")
public class RealisationUniteController {

    @Autowired
    private RealisationUniteRepository realisationUniteRepository;

    /**
     * Récupère les données de réalisation pour toutes les unités d'une période donnée
     * @param pmois Période au format ddMMyyyy
     * @return Liste des données de réalisation
     */
    @GetMapping("/realisation-unite/{pmois}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<RealisationUnite>> getRealisationUnite(@PathVariable String pmois) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<RealisationUnite> result = realisationUniteRepository.findByPmois(date);
        
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * Récupère les données de réalisation pour une unité spécifique
     * @param pmois Période au format ddMMyyyy
     * @param unite Code de l'unité
     * @return Données de réalisation pour l'unité
     */
    @GetMapping("/realisation-unite/{pmois}/{unite}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<RealisationUnite>> getRealisationUniteByUnite(
            @PathVariable String pmois, 
            @PathVariable String unite) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        List<RealisationUnite> result = realisationUniteRepository.findByPmoisAndUnite(date, unite);
        
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        return ResponseEntity.ok(result);
    }

    /**
     * Récupère les données de réalisation pour plusieurs unités
     * @param pmois Période au format ddMMyyyy
     * @param unites Liste des codes d'unités séparés par des virgules
     * @return Données de réalisation pour les unités demandées
     */
    @GetMapping("/realisation-unite/{pmois}/unites/{unites}")
    @JsonView(View.basic.class)
    public ResponseEntity<List<RealisationUnite>> getRealisationUniteByUnites(
            @PathVariable String pmois, 
            @PathVariable String unites) {
        LocalDate date = LocalDate.parse(pmois, DateTimeFormatter.ofPattern("ddMMyyyy"));
        String[] unitesList = unites.split(",");
        List<RealisationUnite> result = realisationUniteRepository.findByPmoisAndUniteIn(date, List.of(unitesList));
        
        if (result.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        return ResponseEntity.ok(result);
    }
}
