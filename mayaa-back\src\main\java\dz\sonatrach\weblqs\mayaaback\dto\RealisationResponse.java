package dz.sonatrach.weblqs.mayaaback.dto;

import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO pour les données de réalisation consolidées
 */
public class RealisationResponse {

    @JsonView(View.basic.class)
    private String periode;

    @JsonView(View.basic.class)
    private LocalDateTime derniereMiseAJour;

    @JsonView(View.basic.class)
    private StatistiquesGeneralesDto statistiques;

    @JsonView(View.basic.class)
    private List<RealisationUniteDto> unites;

    @JsonView(View.basic.class)
    private TotauxRealisationDto totaux;

    @JsonView(View.basic.class)
    private List<EvolutionProductionDto> evolution;

    // Constructeurs
    public RealisationResponse() {
        this.derniereMiseAJour = LocalDateTime.now();
    }

    public RealisationResponse(String periode) {
        this.periode = periode;
        this.derniereMiseAJour = LocalDateTime.now();
    }

    // Méthode utilitaire
    public boolean isEmpty() {
        return (unites == null || unites.isEmpty()) &&
               (totaux == null);
    }

    // Getters et Setters
    public String getPeriode() { return periode; }
    public void setPeriode(String periode) { this.periode = periode; }

    public LocalDateTime getDerniereMiseAJour() { return derniereMiseAJour; }
    public void setDerniereMiseAJour(LocalDateTime derniereMiseAJour) { this.derniereMiseAJour = derniereMiseAJour; }

    public StatistiquesGeneralesDto getStatistiques() { return statistiques; }
    public void setStatistiques(StatistiquesGeneralesDto statistiques) { this.statistiques = statistiques; }

    public List<RealisationUniteDto> getUnites() { return unites; }
    public void setUnites(List<RealisationUniteDto> unites) { this.unites = unites; }

    public TotauxRealisationDto getTotaux() { return totaux; }
    public void setTotaux(TotauxRealisationDto totaux) { this.totaux = totaux; }

    public List<EvolutionProductionDto> getEvolution() { return evolution; }
    public void setEvolution(List<EvolutionProductionDto> evolution) { this.evolution = evolution; }

    /**
     * DTO pour les données de réalisation d'une unité
     */
    public static class RealisationUniteDto {
        @JsonView(View.basic.class)
        private String uniteCode;
        @JsonView(View.basic.class)
        private String unite;
        @JsonView(View.basic.class)
        private String statut;
        @JsonView(View.basic.class)
        private Double productionReelle;
        @JsonView(View.basic.class)
        private Double objectifProduction;
        @JsonView(View.basic.class)
        private Double previsionProduction;
        @JsonView(View.basic.class)
        private Double ecartObjectif;
        @JsonView(View.basic.class)
        private Double tauxRealisationObjectif;
        @JsonView(View.basic.class)
        private Double gnRecu;
        @JsonView(View.basic.class)
        private Double gnTransforme;
        @JsonView(View.basic.class)
        private Double pourcentageGnTransforme;

        public RealisationUniteDto() {}

        // Getters et setters
        public String getUniteCode() { return uniteCode; }
        public void setUniteCode(String uniteCode) { this.uniteCode = uniteCode; }
        public String getUnite() { return unite; }
        public void setUnite(String unite) { this.unite = unite; }
        public String getStatut() { return statut; }
        public void setStatut(String statut) { this.statut = statut; }
        public Double getProductionReelle() { return productionReelle; }
        public void setProductionReelle(Double productionReelle) { this.productionReelle = productionReelle; }
        public Double getObjectifProduction() { return objectifProduction; }
        public void setObjectifProduction(Double objectifProduction) { this.objectifProduction = objectifProduction; }
        public Double getPrevisionProduction() { return previsionProduction; }
        public void setPrevisionProduction(Double previsionProduction) { this.previsionProduction = previsionProduction; }
        public Double getEcartObjectif() { return ecartObjectif; }
        public void setEcartObjectif(Double ecartObjectif) { this.ecartObjectif = ecartObjectif; }
        public Double getTauxRealisationObjectif() { return tauxRealisationObjectif; }
        public void setTauxRealisationObjectif(Double tauxRealisationObjectif) { this.tauxRealisationObjectif = tauxRealisationObjectif; }
        public Double getGnRecu() { return gnRecu; }
        public void setGnRecu(Double gnRecu) { this.gnRecu = gnRecu; }
        public Double getGnTransforme() { return gnTransforme; }
        public void setGnTransforme(Double gnTransforme) { this.gnTransforme = gnTransforme; }
        public Double getPourcentageGnTransforme() { return pourcentageGnTransforme; }
        public void setPourcentageGnTransforme(Double pourcentageGnTransforme) { this.pourcentageGnTransforme = pourcentageGnTransforme; }
    }

    /**
     * DTO pour les totaux de réalisation consolidés
     */
    public static class TotauxRealisationDto {
        @JsonView(View.basic.class)
        private Double totalGnRecu;
        @JsonView(View.basic.class)
        private Double totalGnTransforme;
        @JsonView(View.basic.class)
        private Double pourcentageGlobalGnTransforme;
        @JsonView(View.basic.class)
        private Double totalProductionReelle;
        @JsonView(View.basic.class)
        private Double totalObjectif;
        @JsonView(View.basic.class)
        private Double totalPrevision;
        @JsonView(View.basic.class)
        private Double tauxGlobalRealisationObjectif;

        public TotauxRealisationDto() {}

        // Getters et setters
        public Double getTotalGnRecu() { return totalGnRecu; }
        public void setTotalGnRecu(Double totalGnRecu) { this.totalGnRecu = totalGnRecu; }
        public Double getTotalGnTransforme() { return totalGnTransforme; }
        public void setTotalGnTransforme(Double totalGnTransforme) { this.totalGnTransforme = totalGnTransforme; }
        public Double getPourcentageGlobalGnTransforme() { return pourcentageGlobalGnTransforme; }
        public void setPourcentageGlobalGnTransforme(Double pourcentageGlobalGnTransforme) { this.pourcentageGlobalGnTransforme = pourcentageGlobalGnTransforme; }
        public Double getTotalProductionReelle() { return totalProductionReelle; }
        public void setTotalProductionReelle(Double totalProductionReelle) { this.totalProductionReelle = totalProductionReelle; }
        public Double getTotalObjectif() { return totalObjectif; }
        public void setTotalObjectif(Double totalObjectif) { this.totalObjectif = totalObjectif; }
        public Double getTotalPrevision() { return totalPrevision; }
        public void setTotalPrevision(Double totalPrevision) { this.totalPrevision = totalPrevision; }
        public Double getTauxGlobalRealisationObjectif() { return tauxGlobalRealisationObjectif; }
        public void setTauxGlobalRealisationObjectif(Double tauxGlobalRealisationObjectif) { this.tauxGlobalRealisationObjectif = tauxGlobalRealisationObjectif; }
    }

    /**
     * DTO pour l'évolution mensuelle de la production
     */
    public static class EvolutionProductionDto {
        @JsonView(View.basic.class)
        private String mois;
        @JsonView(View.basic.class)
        private Double production;
        @JsonView(View.basic.class)
        private Double objectif;
        @JsonView(View.basic.class)
        private Double tauxRealisation;

        public EvolutionProductionDto() {}

        public EvolutionProductionDto(String mois, Double production, Double objectif, Double tauxRealisation) {
            this.mois = mois;
            this.production = production;
            this.objectif = objectif;
            this.tauxRealisation = tauxRealisation;
        }

        // Getters et setters
        public String getMois() { return mois; }
        public void setMois(String mois) { this.mois = mois; }
        public Double getProduction() { return production; }
        public void setProduction(Double production) { this.production = production; }
        public Double getObjectif() { return objectif; }
        public void setObjectif(Double objectif) { this.objectif = objectif; }
        public Double getTauxRealisation() { return tauxRealisation; }
        public void setTauxRealisation(Double tauxRealisation) { this.tauxRealisation = tauxRealisation; }
    }

    /**
     * DTO pour les statistiques générales
     */
    public static class StatistiquesGeneralesDto {
        @JsonView(View.basic.class)
        private Integer nombreUnitesActives;
        @JsonView(View.basic.class)
        private Integer nombreUnitesArretTotal;
        @JsonView(View.basic.class)
        private Integer nombreUnitesMaintenance;
        @JsonView(View.basic.class)
        private Integer nombreTotalUnites;

        public StatistiquesGeneralesDto() {}

        public StatistiquesGeneralesDto(Integer nombreUnitesActives, Integer nombreUnitesArretTotal, 
                                      Integer nombreUnitesMaintenance, Integer nombreTotalUnites) {
            this.nombreUnitesActives = nombreUnitesActives;
            this.nombreUnitesArretTotal = nombreUnitesArretTotal;
            this.nombreUnitesMaintenance = nombreUnitesMaintenance;
            this.nombreTotalUnites = nombreTotalUnites;
        }

        // Getters et setters
        public Integer getNombreUnitesActives() { return nombreUnitesActives; }
        public void setNombreUnitesActives(Integer nombreUnitesActives) { this.nombreUnitesActives = nombreUnitesActives; }
        public Integer getNombreUnitesArretTotal() { return nombreUnitesArretTotal; }
        public void setNombreUnitesArretTotal(Integer nombreUnitesArretTotal) { this.nombreUnitesArretTotal = nombreUnitesArretTotal; }
        public Integer getNombreUnitesMaintenance() { return nombreUnitesMaintenance; }
        public void setNombreUnitesMaintenance(Integer nombreUnitesMaintenance) { this.nombreUnitesMaintenance = nombreUnitesMaintenance; }
        public Integer getNombreTotalUnites() { return nombreTotalUnites; }
        public void setNombreTotalUnites(Integer nombreTotalUnites) { this.nombreTotalUnites = nombreTotalUnites; }
    }
}
