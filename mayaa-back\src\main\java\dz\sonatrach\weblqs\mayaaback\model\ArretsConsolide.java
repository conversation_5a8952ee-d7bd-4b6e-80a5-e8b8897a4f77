package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;

/**
 * Modèle pour les données consolidées des arrêts
 * Agrège les données de toutes les unités pour une vue globale
 */
public class ArretsConsolide implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonView(View.basic.class)
    private LocalDate mois;

    @JsonView(View.basic.class)
    private String periode;

    // Synthèse globale
    @JsonView(View.basic.class)
    private List<SyntheseArretsConsolide> syntheseGlobale;

    // Répartition par siège consolidée
    @JsonView(View.basic.class)
    private List<RepartitionSiegeConsolide> repartitionSiege;

    // Répartition par cause consolidée
    @JsonView(View.basic.class)
    private List<RepartitionCauseConsolide> repartitionCause;

    // Situation des trains consolidée
    @JsonView(View.basic.class)
    private List<SituationTrainsConsolide> situationTrains;

    // Statistiques globales
    @JsonView(View.basic.class)
    private StatistiquesGlobales statistiques;

    // Constructeurs
    public ArretsConsolide() {}

    public ArretsConsolide(LocalDate mois) {
        this.mois = mois;
    }

    // Classes internes pour les données consolidées
    public static class SyntheseArretsConsolide implements Serializable {
        @JsonView(View.basic.class)
        private String unite;
        
        @JsonView(View.basic.class)
        private Integer totalArrets;
        
        @JsonView(View.basic.class)
        private BigDecimal mapPourcentage;
        
        @JsonView(View.basic.class)
        private BigDecimal autoconsommationNette;
        
        @JsonView(View.basic.class)
        private BigDecimal gazTorche;
        
        @JsonView(View.basic.class)
        private Integer trainsEnService;

        // Constructeurs et getters/setters
        public SyntheseArretsConsolide() {}

        public String getUnite() { return unite; }
        public void setUnite(String unite) { this.unite = unite; }
        
        public Integer getTotalArrets() { return totalArrets; }
        public void setTotalArrets(Integer totalArrets) { this.totalArrets = totalArrets; }
        
        public BigDecimal getMapPourcentage() { return mapPourcentage; }
        public void setMapPourcentage(BigDecimal mapPourcentage) { this.mapPourcentage = mapPourcentage; }
        
        public BigDecimal getAutoconsommationNette() { return autoconsommationNette; }
        public void setAutoconsommationNette(BigDecimal autoconsommationNette) { this.autoconsommationNette = autoconsommationNette; }
        
        public BigDecimal getGazTorche() { return gazTorche; }
        public void setGazTorche(BigDecimal gazTorche) { this.gazTorche = gazTorche; }
        
        public Integer getTrainsEnService() { return trainsEnService; }
        public void setTrainsEnService(Integer trainsEnService) { this.trainsEnService = trainsEnService; }
    }

    public static class RepartitionSiegeConsolide implements Serializable {
        @JsonView(View.basic.class)
        private String siegeCause;
        
        @JsonView(View.basic.class)
        private String typeSiege;
        
        @JsonView(View.basic.class)
        private Integer totalArrets;
        
        @JsonView(View.basic.class)
        private BigDecimal pourcentageGlobal;

        // Constructeurs et getters/setters
        public RepartitionSiegeConsolide() {}

        public String getSiegeCause() { return siegeCause; }
        public void setSiegeCause(String siegeCause) { this.siegeCause = siegeCause; }
        
        public String getTypeSiege() { return typeSiege; }
        public void setTypeSiege(String typeSiege) { this.typeSiege = typeSiege; }
        
        public Integer getTotalArrets() { return totalArrets; }
        public void setTotalArrets(Integer totalArrets) { this.totalArrets = totalArrets; }
        
        public BigDecimal getPourcentageGlobal() { return pourcentageGlobal; }
        public void setPourcentageGlobal(BigDecimal pourcentageGlobal) { this.pourcentageGlobal = pourcentageGlobal; }
    }

    public static class RepartitionCauseConsolide implements Serializable {
        @JsonView(View.basic.class)
        private String cause;
        
        @JsonView(View.basic.class)
        private String typeCause;
        
        @JsonView(View.basic.class)
        private Integer totalArrets;
        
        @JsonView(View.basic.class)
        private BigDecimal pourcentageGlobal;

        // Constructeurs et getters/setters
        public RepartitionCauseConsolide() {}

        public String getCause() { return cause; }
        public void setCause(String cause) { this.cause = cause; }
        
        public String getTypeCause() { return typeCause; }
        public void setTypeCause(String typeCause) { this.typeCause = typeCause; }
        
        public Integer getTotalArrets() { return totalArrets; }
        public void setTotalArrets(Integer totalArrets) { this.totalArrets = totalArrets; }
        
        public BigDecimal getPourcentageGlobal() { return pourcentageGlobal; }
        public void setPourcentageGlobal(BigDecimal pourcentageGlobal) { this.pourcentageGlobal = pourcentageGlobal; }
    }

    public static class SituationTrainsConsolide implements Serializable {
        @JsonView(View.basic.class)
        private String typeCause;
        
        @JsonView(View.basic.class)
        private Integer totalArrets;
        
        @JsonView(View.basic.class)
        private String causesPrincipales;
        
        @JsonView(View.basic.class)
        private String analyseGlobale;

        // Constructeurs et getters/setters
        public SituationTrainsConsolide() {}

        public String getTypeCause() { return typeCause; }
        public void setTypeCause(String typeCause) { this.typeCause = typeCause; }
        
        public Integer getTotalArrets() { return totalArrets; }
        public void setTotalArrets(Integer totalArrets) { this.totalArrets = totalArrets; }
        
        public String getCausesPrincipales() { return causesPrincipales; }
        public void setCausesPrincipales(String causesPrincipales) { this.causesPrincipales = causesPrincipales; }
        
        public String getAnalyseGlobale() { return analyseGlobale; }
        public void setAnalyseGlobale(String analyseGlobale) { this.analyseGlobale = analyseGlobale; }
    }

    public static class StatistiquesGlobales implements Serializable {
        @JsonView(View.basic.class)
        private Integer totalArretsGlobal;
        
        @JsonView(View.basic.class)
        private Integer nombreUnites;
        
        @JsonView(View.basic.class)
        private BigDecimal moyenneMapPourcentage;
        
        @JsonView(View.basic.class)
        private BigDecimal moyenneAutoconsommation;
        
        @JsonView(View.basic.class)
        private BigDecimal moyenneGazTorche;
        
        @JsonView(View.basic.class)
        private Integer totalTrainsEnService;

        // Constructeurs et getters/setters
        public StatistiquesGlobales() {}

        public Integer getTotalArretsGlobal() { return totalArretsGlobal; }
        public void setTotalArretsGlobal(Integer totalArretsGlobal) { this.totalArretsGlobal = totalArretsGlobal; }
        
        public Integer getNombreUnites() { return nombreUnites; }
        public void setNombreUnites(Integer nombreUnites) { this.nombreUnites = nombreUnites; }
        
        public BigDecimal getMoyenneMapPourcentage() { return moyenneMapPourcentage; }
        public void setMoyenneMapPourcentage(BigDecimal moyenneMapPourcentage) { this.moyenneMapPourcentage = moyenneMapPourcentage; }
        
        public BigDecimal getMoyenneAutoconsommation() { return moyenneAutoconsommation; }
        public void setMoyenneAutoconsommation(BigDecimal moyenneAutoconsommation) { this.moyenneAutoconsommation = moyenneAutoconsommation; }
        
        public BigDecimal getMoyenneGazTorche() { return moyenneGazTorche; }
        public void setMoyenneGazTorche(BigDecimal moyenneGazTorche) { this.moyenneGazTorche = moyenneGazTorche; }
        
        public Integer getTotalTrainsEnService() { return totalTrainsEnService; }
        public void setTotalTrainsEnService(Integer totalTrainsEnService) { this.totalTrainsEnService = totalTrainsEnService; }
    }

    // Getters et Setters principaux
    public LocalDate getMois() { return mois; }
    public void setMois(LocalDate mois) { this.mois = mois; }
    
    public String getPeriode() { return periode; }
    public void setPeriode(String periode) { this.periode = periode; }
    
    public List<SyntheseArretsConsolide> getSyntheseGlobale() { return syntheseGlobale; }
    public void setSyntheseGlobale(List<SyntheseArretsConsolide> syntheseGlobale) { this.syntheseGlobale = syntheseGlobale; }
    
    public List<RepartitionSiegeConsolide> getRepartitionSiege() { return repartitionSiege; }
    public void setRepartitionSiege(List<RepartitionSiegeConsolide> repartitionSiege) { this.repartitionSiege = repartitionSiege; }
    
    public List<RepartitionCauseConsolide> getRepartitionCause() { return repartitionCause; }
    public void setRepartitionCause(List<RepartitionCauseConsolide> repartitionCause) { this.repartitionCause = repartitionCause; }
    
    public List<SituationTrainsConsolide> getSituationTrains() { return situationTrains; }
    public void setSituationTrains(List<SituationTrainsConsolide> situationTrains) { this.situationTrains = situationTrains; }
    
    public StatistiquesGlobales getStatistiques() { return statistiques; }
    public void setStatistiques(StatistiquesGlobales statistiques) { this.statistiques = statistiques; }
}
