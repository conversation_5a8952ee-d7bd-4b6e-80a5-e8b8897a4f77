package dz.sonatrach.weblqs.mayaaback.model;

import com.fasterxml.jackson.annotation.JsonView;
import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * Entité représentant les données de la vue LIST_UNITE
 * Contient les informations sur les unités avec leurs objectifs et taux design d'autoconsommation
 */
@Entity
@Table(name = "LIST_UNITE")
@NamedQuery(name = "ListUnite.findAll", query = "SELECT l FROM ListUnite l")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class ListUnite implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "CODE_UNITE")
    @JsonView(View.basic.class)
    private String codeUnite;

    @Column(name = "UNITE")
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "PMOIS")
    @JsonView(View.basic.class)
    private LocalDate pmois;

    @Column(name = "TAUX_AC_OBJ")
    @JsonView(View.basic.class)
    private BigDecimal tauxAcObj;

    @Column(name = "TAUX_AC_DESIGN")
    @JsonView(View.basic.class)
    private BigDecimal tauxAcDesign;

    // Constructeurs
    public ListUnite() {}

    public ListUnite(Long id, String codeUnite, String unite, LocalDate pmois, 
                     BigDecimal tauxAcObj, BigDecimal tauxAcDesign) {
        this.id = id;
        this.codeUnite = codeUnite;
        this.unite = unite;
        this.pmois = pmois;
        this.tauxAcObj = tauxAcObj;
        this.tauxAcDesign = tauxAcDesign;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeUnite() {
        return codeUnite;
    }

    public void setCodeUnite(String codeUnite) {
        this.codeUnite = codeUnite;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getPmois() {
        return pmois;
    }

    public void setPmois(LocalDate pmois) {
        this.pmois = pmois;
    }

    public BigDecimal getTauxAcObj() {
        return tauxAcObj;
    }

    public void setTauxAcObj(BigDecimal tauxAcObj) {
        this.tauxAcObj = tauxAcObj;
    }

    public BigDecimal getTauxAcDesign() {
        return tauxAcDesign;
    }

    public void setTauxAcDesign(BigDecimal tauxAcDesign) {
        this.tauxAcDesign = tauxAcDesign;
    }
}
