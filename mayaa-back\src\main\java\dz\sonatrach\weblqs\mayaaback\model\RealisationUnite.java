package dz.sonatrach.weblqs.mayaaback.model;

import dz.sonatrach.weblqs.mayaaback.views.View;
import com.fasterxml.jackson.annotation.JsonView;
import jakarta.persistence.*;
import java.time.LocalDate;

/**
 * Entité pour la vue REALISATION_UNITE
 * Contient les données de réalisation et performance des unités
 */
@Entity
@Table(name = "REALISATION_UNITE")
public class RealisationUnite {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE_CODE")
    @JsonView(View.basic.class)
    private String uniteCode;

    @Column(name = "UNITE")
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "PMOIS")
    @JsonView(View.basic.class)
    private LocalDate pmois;

    @Column(name = "OBJECTIF_PRODUCTION_GNL")
    @JsonView(View.basic.class)
    private Double objectifProductionGnl;

    @Column(name = "PREVISION_PRODUCTION_GNL")
    @JsonView(View.basic.class)
    private Double previsionProductionGnl;

    @Column(name = "REEL_PRODUCTION_GNL")
    @JsonView(View.basic.class)
    private Double reelProductionGnl;

    @Column(name = "STATUT")
    @JsonView(View.basic.class)
    private String statut;

    // Constructeurs
    public RealisationUnite() {}

    public RealisationUnite(String uniteCode, String unite, LocalDate pmois, 
                           Double objectifProductionGnl, Double previsionProductionGnl, 
                           Double reelProductionGnl, String statut) {
        this.uniteCode = uniteCode;
        this.unite = unite;
        this.pmois = pmois;
        this.objectifProductionGnl = objectifProductionGnl;
        this.previsionProductionGnl = previsionProductionGnl;
        this.reelProductionGnl = reelProductionGnl;
        this.statut = statut;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUniteCode() {
        return uniteCode;
    }

    public void setUniteCode(String uniteCode) {
        this.uniteCode = uniteCode;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public LocalDate getPmois() {
        return pmois;
    }

    public void setPmois(LocalDate pmois) {
        this.pmois = pmois;
    }

    public Double getObjectifProductionGnl() {
        return objectifProductionGnl;
    }

    public void setObjectifProductionGnl(Double objectifProductionGnl) {
        this.objectifProductionGnl = objectifProductionGnl;
    }

    public Double getPrevisionProductionGnl() {
        return previsionProductionGnl;
    }

    public void setPrevisionProductionGnl(Double previsionProductionGnl) {
        this.previsionProductionGnl = previsionProductionGnl;
    }

    public Double getReelProductionGnl() {
        return reelProductionGnl;
    }

    public void setReelProductionGnl(Double reelProductionGnl) {
        this.reelProductionGnl = reelProductionGnl;
    }

    public String getStatut() {
        return statut;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    @Override
    public String toString() {
        return "RealisationUnite{" +
                "id=" + id +
                ", uniteCode='" + uniteCode + '\'' +
                ", unite='" + unite + '\'' +
                ", pmois=" + pmois +
                ", objectifProductionGnl=" + objectifProductionGnl +
                ", previsionProductionGnl=" + previsionProductionGnl +
                ", reelProductionGnl=" + reelProductionGnl +
                ", statut='" + statut + '\'' +
                '}';
    }
}
