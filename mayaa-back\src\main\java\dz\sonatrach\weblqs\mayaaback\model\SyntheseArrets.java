package dz.sonatrach.weblqs.mayaaback.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonView;

import dz.sonatrach.weblqs.mayaaback.views.View;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Entité pour la synthèse des arrêts par train
 * Représente les données du tableau "Synthèse des arrêts, MAP, AC nette, GT par trains"
 */
@Entity
@Table(name = "SYNTHESE_ARRETS")
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@ToString
public class SyntheseArrets implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @JsonView(View.basic.class)
    private Long id;

    @Column(name = "UNITE", length = 10)
    @JsonView(View.basic.class)
    private String unite;

    @Column(name = "CODE_TRAIN", length = 10)
    @JsonView(View.basic.class)
    private String codeTrain;

    @Column(name = "MOIS")
    @JsonView(View.basic.class)
    private LocalDate mois;

    // Colonnes pour les heures
    @Column(name = "NBRE_HEURES")
    @JsonView(View.basic.class)
    private BigDecimal nbreHeures;

    @Column(name = "MAP")
    @JsonView(View.basic.class)
    private BigDecimal map;

    // Colonnes AC nette
    @Column(name = "AC_NETTE")
    @JsonView(View.basic.class)
    private BigDecimal acNette;

    // Colonnes GT (Gaz Torché)
    @Column(name = "GT_INTERNE")
    @JsonView(View.basic.class)
    private BigDecimal gtInterne;

    @Column(name = "GT_EXTERNE")
    @JsonView(View.basic.class)
    private BigDecimal gtExterne;

    // Colonnes pour les durées d'arrêt
    @Column(name = "PLUS_24H_INTERNE")
    @JsonView(View.basic.class)
    private BigDecimal plus24hInterne;

    @Column(name = "PLUS_24H_EXTERNE")
    @JsonView(View.basic.class)
    private BigDecimal plus24hExterne;

    @Column(name = "MOINS_24H_INTERNE")
    @JsonView(View.basic.class)
    private BigDecimal moins24hInterne;

    @Column(name = "MOINS_24H_EXTERNE")
    @JsonView(View.basic.class)
    private BigDecimal moins24hExterne;

    @Column(name = "MOINS_5H_INTERNE")
    @JsonView(View.basic.class)
    private BigDecimal moins5hInterne;

    @Column(name = "MOINS_5H_EXTERNE")
    @JsonView(View.basic.class)
    private BigDecimal moins5hExterne;

    // Colonnes pour la répartition par type
    @Column(name = "SID")
    @JsonView(View.basic.class)
    private BigDecimal sid;

    @Column(name = "DEI")
    @JsonView(View.basic.class)
    private BigDecimal dei;

    @Column(name = "AV")
    @JsonView(View.basic.class)
    private BigDecimal av;

    @Column(name = "AP")
    @JsonView(View.basic.class)
    private BigDecimal ap;

    // Constructeurs
    public SyntheseArrets() {}

    public SyntheseArrets(String unite, String codeTrain, LocalDate mois) {
        this.unite = unite;
        this.codeTrain = codeTrain;
        this.mois = mois;
    }

    // Getters et Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnite() {
        return unite;
    }

    public void setUnite(String unite) {
        this.unite = unite;
    }

    public String getCodeTrain() {
        return codeTrain;
    }

    public void setCodeTrain(String codeTrain) {
        this.codeTrain = codeTrain;
    }

    public LocalDate getMois() {
        return mois;
    }

    public void setMois(LocalDate mois) {
        this.mois = mois;
    }

    public BigDecimal getNbreHeures() {
        return nbreHeures;
    }

    public void setNbreHeures(BigDecimal nbreHeures) {
        this.nbreHeures = nbreHeures;
    }

    public BigDecimal getMap() {
        return map;
    }

    public void setMap(BigDecimal map) {
        this.map = map;
    }

    public BigDecimal getAcNette() {
        return acNette;
    }

    public void setAcNette(BigDecimal acNette) {
        this.acNette = acNette;
    }

    public BigDecimal getGtInterne() {
        return gtInterne;
    }

    public void setGtInterne(BigDecimal gtInterne) {
        this.gtInterne = gtInterne;
    }

    public BigDecimal getGtExterne() {
        return gtExterne;
    }

    public void setGtExterne(BigDecimal gtExterne) {
        this.gtExterne = gtExterne;
    }

    public BigDecimal getPlus24hInterne() {
        return plus24hInterne;
    }

    public void setPlus24hInterne(BigDecimal plus24hInterne) {
        this.plus24hInterne = plus24hInterne;
    }

    public BigDecimal getPlus24hExterne() {
        return plus24hExterne;
    }

    public void setPlus24hExterne(BigDecimal plus24hExterne) {
        this.plus24hExterne = plus24hExterne;
    }

    public BigDecimal getMoins24hInterne() {
        return moins24hInterne;
    }

    public void setMoins24hInterne(BigDecimal moins24hInterne) {
        this.moins24hInterne = moins24hInterne;
    }

    public BigDecimal getMoins24hExterne() {
        return moins24hExterne;
    }

    public void setMoins24hExterne(BigDecimal moins24hExterne) {
        this.moins24hExterne = moins24hExterne;
    }

    public BigDecimal getMoins5hInterne() {
        return moins5hInterne;
    }

    public void setMoins5hInterne(BigDecimal moins5hInterne) {
        this.moins5hInterne = moins5hInterne;
    }

    public BigDecimal getMoins5hExterne() {
        return moins5hExterne;
    }

    public void setMoins5hExterne(BigDecimal moins5hExterne) {
        this.moins5hExterne = moins5hExterne;
    }

    public BigDecimal getSid() {
        return sid;
    }

    public void setSid(BigDecimal sid) {
        this.sid = sid;
    }

    public BigDecimal getDei() {
        return dei;
    }

    public void setDei(BigDecimal dei) {
        this.dei = dei;
    }

    public BigDecimal getAv() {
        return av;
    }

    public void setAv(BigDecimal av) {
        this.av = av;
    }

    public BigDecimal getAp() {
        return ap;
    }

    public void setAp(BigDecimal ap) {
        this.ap = ap;
    }
}
