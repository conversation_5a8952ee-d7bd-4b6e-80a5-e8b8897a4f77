package dz.sonatrach.weblqs.mayaaback.repo;

import dz.sonatrach.weblqs.mayaaback.model.ListUnite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'accès aux données de la vue LIST_UNITE
 */
@Repository
public interface ListUniteRepository extends JpaRepository<ListUnite, Long> {

    /**
     * Trouve toutes les unités pour une période donnée
     * @param pmois La période de référence
     * @return Liste des unités pour cette période
     */
    List<ListUnite> findByPmois(LocalDate pmois);

    /**
     * Trouve une unité spécifique pour une période donnée
     * @param codeUnite Le code de l'unité
     * @param pmois La période de référence
     * @return L'unité trouvée ou Optional.empty()
     */
    Optional<ListUnite> findByCodeUniteAndPmois(String codeUnite, LocalDate pmois);

    /**
     * Trouve toutes les données pour une unité donnée, triées par période décroissante
     * @param codeUnite Le code de l'unité
     * @return Liste des données pour cette unité
     */
    List<ListUnite> findByCodeUniteOrderByPmoisDesc(String codeUnite);

    /**
     * Trouve toutes les unités, triées par période décroissante puis par code d'unité
     * @return Liste de toutes les unités
     */
    List<ListUnite> findAllByOrderByPmoisDescCodeUniteAsc();

    /**
     * Trouve la dernière période disponible pour une unité donnée
     * @param codeUnite Le code de l'unité
     * @return La dernière donnée disponible pour cette unité
     */
    Optional<ListUnite> findFirstByCodeUniteOrderByPmoisDesc(String codeUnite);
}
