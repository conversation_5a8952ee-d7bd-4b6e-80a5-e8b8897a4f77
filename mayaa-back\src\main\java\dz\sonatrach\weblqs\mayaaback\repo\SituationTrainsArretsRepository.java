package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.SituationTrainsArrets;

/**
 * Repository pour l'accès aux données de la vue SITUATION_TRAINS_ARRETS
 */
@Repository
public interface SituationTrainsArretsRepository extends JpaRepository<SituationTrainsArrets, Long> {

    /**
     * Récupère la situation des trains pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de situation des trains
     */
    List<SituationTrainsArrets> findByUniteAndMois(String unite, LocalDate mois);

    /**
     * Récupère la situation des trains pour un type de cause spécifique
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param typeCause Type de cause (INTERNES ou EXTERNES)
     * @return Données de situation pour le type de cause
     */
    Optional<SituationTrainsArrets> findByUniteAndMoisAndTypeCause(String unite, LocalDate mois, String typeCause);

    /**
     * Récupère les causes principales internes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Données des causes principales internes
     */
    @Query("SELECT s FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois = :mois AND s.typeCause = 'INTERNES'")
    Optional<SituationTrainsArrets> findCausesInternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les causes principales externes pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Données des causes principales externes
     */
    @Query("SELECT s FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois = :mois AND s.typeCause = 'EXTERNES'")
    Optional<SituationTrainsArrets> findCausesExternesByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données d'analyse du complexe pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Données d'analyse du complexe
     */
    @Query("SELECT s FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois = :mois AND s.autoconsommationPourcentage IS NOT NULL")
    Optional<SituationTrainsArrets> findAnalyseComplexeByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère le nombre total d'arrêts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Nombre total d'arrêts
     */
    @Query("SELECT SUM(s.nombreArrets) FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois = :mois")
    Integer getTotalArretsByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données pour une période donnée (pour l'évolution)
     * @param unite Code de l'unité
     * @param dateDebut Date de début de la période
     * @param dateFin Date de fin de la période
     * @return Liste des données sur la période
     */
    @Query("SELECT s FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois BETWEEN :dateDebut AND :dateFin ORDER BY s.mois, s.typeCause")
    List<SituationTrainsArrets> findByUniteAndMoisBetween(@Param("unite") String unite, 
                                                          @Param("dateDebut") LocalDate dateDebut, 
                                                          @Param("dateFin") LocalDate dateFin);

    /**
     * Récupère les causes récurrentes pour une unité et une période donnée
     * @param unite Code de l'unité
     * @param dateDebut Date de début de la période
     * @param dateFin Date de fin de la période
     * @return Liste des causes récurrentes
     */
    @Query("SELECT DISTINCT s.causesRecurrentes FROM SituationTrainsArrets s WHERE s.unite = :unite AND s.mois BETWEEN :dateDebut AND :dateFin AND s.causesRecurrentes IS NOT NULL")
    List<String> findCausesRecurrentesByUniteAndPeriode(@Param("unite") String unite, 
                                                        @Param("dateDebut") LocalDate dateDebut, 
                                                        @Param("dateFin") LocalDate dateFin);
}
