package dz.sonatrach.weblqs.mayaaback.repo;

import java.time.LocalDate;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import dz.sonatrach.weblqs.mayaaback.model.SyntheseArrets;

/**
 * Repository pour l'accès aux données de la vue SYNTHESE_ARRETS
 */
@Repository
public interface SyntheseArretsRepository extends JpaRepository<SyntheseArrets, Long> {

    /**
     * Récupère la synthèse des arrêts pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des données de synthèse des arrêts
     */
    List<SyntheseArrets> findByUniteAndMois(String unite, LocalDate mois);

    /**
     * R<PERSON>cupère la synthèse des arrêts pour une unité, un mois et un train spécifique
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @param codeTrain Code du train
     * @return Liste des données de synthèse des arrêts pour le train
     */
    List<SyntheseArrets> findByUniteAndMoisAndCodeTrain(String unite, LocalDate mois, String codeTrain);

    /**
     * Récupère les trains disponibles pour une unité et un mois donnés
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Liste des codes de trains distincts
     */
    @Query("SELECT DISTINCT s.codeTrain FROM SyntheseArrets s WHERE s.unite = :unite AND s.mois = :mois ORDER BY s.codeTrain")
    List<String> findDistinctTrainsByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données agrégées pour l'ensemble de l'unité (tous trains confondus)
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Données agrégées pour l'unité complète
     */
    @Query("SELECT new SyntheseArrets(s.unite, 'COMPLEXE', s.mois) " +
           "FROM SyntheseArrets s WHERE s.unite = :unite AND s.mois = :mois")
    List<SyntheseArrets> findAggregatedByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les totaux par type d'arrêt pour une unité et un mois
     * @param unite Code de l'unité
     * @param mois Mois de référence
     * @return Données totalisées par type
     */
    @Query("SELECT s FROM SyntheseArrets s WHERE s.unite = :unite AND s.mois = :mois AND s.codeTrain = 'TOTAL'")
    List<SyntheseArrets> findTotauxByUniteAndMois(@Param("unite") String unite, @Param("mois") LocalDate mois);

    /**
     * Récupère les données pour une période donnée (pour l'évolution)
     * @param unite Code de l'unité
     * @param dateDebut Date de début de la période
     * @param dateFin Date de fin de la période
     * @return Liste des données sur la période
     */
    @Query("SELECT s FROM SyntheseArrets s WHERE s.unite = :unite AND s.mois BETWEEN :dateDebut AND :dateFin ORDER BY s.mois")
    List<SyntheseArrets> findByUniteAndMoisBetween(@Param("unite") String unite, 
                                                   @Param("dateDebut") LocalDate dateDebut, 
                                                   @Param("dateFin") LocalDate dateFin);
}
