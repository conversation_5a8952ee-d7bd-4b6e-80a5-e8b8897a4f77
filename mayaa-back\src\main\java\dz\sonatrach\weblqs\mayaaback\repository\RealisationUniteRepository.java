package dz.sonatrach.weblqs.mayaaback.repository;

import dz.sonatrach.weblqs.mayaaback.model.RealisationUnite;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository pour l'accès aux données de la vue REALISATION_UNITE
 */
@Repository
public interface RealisationUniteRepository extends JpaRepository<RealisationUnite, Long> {

    /**
     * Récupère toutes les données de réalisation pour une période donnée
     * @param pmois Période
     * @return Liste des données de réalisation
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.pmois = :pmois ORDER BY r.uniteCode")
    List<RealisationUnite> findByPmois(@Param("pmois") LocalDate pmois);

    /**
     * Récupère les données de réalisation pour une unité et une période données
     * @param pmois Période
     * @param unite Code de l'unité
     * @return Liste des données de réalisation
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.pmois = :pmois AND r.uniteCode = :unite")
    List<RealisationUnite> findByPmoisAndUnite(@Param("pmois") LocalDate pmois, @Param("unite") String unite);

    /**
     * Récupère les données de réalisation pour plusieurs unités et une période données
     * @param pmois Période
     * @param unites Liste des codes d'unités
     * @return Liste des données de réalisation
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.pmois = :pmois AND r.uniteCode IN :unites ORDER BY r.uniteCode")
    List<RealisationUnite> findByPmoisAndUniteIn(@Param("pmois") LocalDate pmois, @Param("unites") List<String> unites);

    /**
     * Récupère les données de réalisation pour une unité sur plusieurs périodes
     * @param unite Code de l'unité
     * @return Liste des données de réalisation triées par période décroissante
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.uniteCode = :unite ORDER BY r.pmois DESC")
    List<RealisationUnite> findByUniteOrderByPmoisDesc(@Param("unite") String unite);

    /**
     * Récupère les dernières données de réalisation pour une unité
     * @param unite Code de l'unité
     * @return Dernières données de réalisation
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.uniteCode = :unite ORDER BY r.pmois DESC LIMIT 1")
    RealisationUnite findLatestByUnite(@Param("unite") String unite);

    /**
     * Récupère les données de réalisation pour toutes les unités actives
     * @param pmois Période
     * @return Liste des données de réalisation pour les unités actives
     */
    @Query("SELECT r FROM RealisationUnite r WHERE r.pmois = :pmois AND r.statut = 'ACTIF' ORDER BY r.uniteCode")
    List<RealisationUnite> findActiveByPmois(@Param("pmois") LocalDate pmois);
}
