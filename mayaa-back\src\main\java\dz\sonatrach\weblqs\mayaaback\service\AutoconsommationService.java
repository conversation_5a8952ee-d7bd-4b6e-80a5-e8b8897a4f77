package dz.sonatrach.weblqs.mayaaback.service;

import dz.sonatrach.weblqs.mayaaback.dto.AutoconsommationResponse;
import dz.sonatrach.weblqs.mayaaback.dto.AutoconsommationResponse.*;
import dz.sonatrach.weblqs.mayaaback.model.AutoConsMens;
import dz.sonatrach.weblqs.mayaaback.model.GazTorcheeParCauseTorchage;
import dz.sonatrach.weblqs.mayaaback.repo.AutoConsMensuelRepository;
import dz.sonatrach.weblqs.mayaaback.repo.GazTorcheeParCauseTorchageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Service pour les données d'autoconsommation
 */
@Service
public class AutoconsommationService {

    @Autowired
    private AutoConsMensuelRepository autoConsMensuelRepository;

    @Autowired
    private GazTorcheeParCauseTorchageRepository gazTorcheeRepository;

    private static final List<String> UNITES_PRINCIPALES = Arrays.asList("5X1", "5X2", "5X3", "5X8");

    /**
     * Récupère toutes les données d'autoconsommation consolidées
     */
    public AutoconsommationResponse getDonneesAutoconsommation(LocalDate pmois) {
        String pmoisStr = pmois.format(DateTimeFormatter.ofPattern("ddMMyyyy"));
        AutoconsommationResponse response = new AutoconsommationResponse(pmoisStr);

        try {
            System.out.println("=== SERVICE AUTOCONSOMMATION - DONNEES CONSOLIDEES ===");
            System.out.println("Période: " + pmoisStr);

            // Récupération des données
            response.setStatistiques(getStatistiquesAutoconsommation(pmois));
            response.setUnites(getAutoconsommationParUnite(pmois));
            response.setGazTorcheCauses(getGazTorcheParCause(pmois));
            response.setTotaux(getTotauxAutoconsommation(pmois));
            response.setEvolution(getEvolutionAutoconsommation(pmois, 6));

            System.out.println("Données d'autoconsommation consolidées avec succès");
            return response;

        } catch (Exception e) {
            System.err.println("Erreur lors de la consolidation des données d'autoconsommation: " + e.getMessage());
            e.printStackTrace();
            return response; // Retourne une réponse vide plutôt que null
        }
    }

    /**
     * Récupère les statistiques d'autoconsommation
     */
    public StatistiquesAutoconsommationDto getStatistiquesAutoconsommation(LocalDate pmois) {
        try {
            System.out.println("=== CALCUL STATISTIQUES AUTOCONSOMMATION ===");
            
            List<AutoconsommationUniteDto> unites = getAutoconsommationParUnite(pmois);
            
            int nombreUnitesAcceptables = 0;
            int nombreUnitesASurveiller = 0;
            int nombreUnitesCritiques = 0;
            
            for (AutoconsommationUniteDto unite : unites) {
                Double tauxAC = unite.getTauxAutoconsommationNette();
                if (tauxAC != null) {
                    if (tauxAC <= 5.0) {
                        nombreUnitesAcceptables++;
                    } else if (tauxAC <= 8.0) {
                        nombreUnitesASurveiller++;
                    } else {
                        nombreUnitesCritiques++;
                    }
                }
            }
            
            String performanceGlobale = "ACCEPTABLE";
            if (nombreUnitesCritiques > 0) {
                performanceGlobale = "CRITIQUE";
            } else if (nombreUnitesASurveiller > 0) {
                performanceGlobale = "A_SURVEILLER";
            }
            
            StatistiquesAutoconsommationDto statistiques = new StatistiquesAutoconsommationDto(
                nombreUnitesAcceptables, nombreUnitesASurveiller, nombreUnitesCritiques, performanceGlobale
            );
            
            System.out.println("Statistiques AC calculées: " + nombreUnitesAcceptables + " acceptables, " + 
                             nombreUnitesASurveiller + " à surveiller, " + nombreUnitesCritiques + " critiques");
            
            return statistiques;
            
        } catch (Exception e) {
            System.err.println("Erreur lors du calcul des statistiques d'autoconsommation: " + e.getMessage());
            e.printStackTrace();
            return new StatistiquesAutoconsommationDto(0, 0, 0, "INCONNU");
        }
    }

    /**
     * Récupère les données d'autoconsommation par unité
     */
    public List<AutoconsommationUniteDto> getAutoconsommationParUnite(LocalDate pmois) {
        try {
            System.out.println("=== DONNEES AUTOCONSOMMATION PAR UNITE ===");

            List<AutoConsMens> autocons = autoConsMensuelRepository.findByPmois(pmois);
            List<AutoconsommationUniteDto> unites = new ArrayList<>();
            
            for (AutoConsMens ac : autocons) {
                if (UNITES_PRINCIPALES.contains(ac.getUnite())) {
                    AutoconsommationUniteDto unite = new AutoconsommationUniteDto();
                    
                    unite.setUniteCode(ac.getUnite());
                    unite.setUnite("Unité " + ac.getUnite());
                    unite.setStatut(determinerStatutUniteAC(ac));
                    
                    // Données de base
                    unite.setGnRecu(ac.getReceptionGnMois() != null ? ac.getReceptionGnMois().doubleValue() : 0.0);
                    // Estimation de l'autoconsommation globale basée sur les taux
                    double gnRecu = unite.getGnRecu();
                    double autoconsGlobale = gnRecu * 0.06; // Estimation 6%
                    unite.setAutoconsommationGlobale(autoconsGlobale);
                    unite.setAutoconsommationNette(ac.getAutoConsMoisNet() != null ? ac.getAutoConsMoisNet().doubleValue() : 0.0);

                    // Gaz torché (à adapter selon vos données)
                    unite.setGazTorcheTotal(ac.getGazTorcheeMois() != null ? ac.getGazTorcheeMois().doubleValue() : 0.0);
                    unite.setGazTorcheInterne(unite.getGazTorcheTotal() * 0.7); // Estimation
                    unite.setGazTorcheExterne(unite.getGazTorcheTotal() * 0.3); // Estimation
                    
                    // Calculs des taux
                    if (unite.getGnRecu() > 0) {
                        unite.setTauxAutoconsommationGlobale((unite.getAutoconsommationGlobale() / unite.getGnRecu()) * 100);
                        unite.setTauxAutoconsommationNette((unite.getAutoconsommationNette() / unite.getGnRecu()) * 100);
                        unite.setTauxGazTorche((unite.getGazTorcheTotal() / unite.getGnRecu()) * 100);
                    } else {
                        unite.setTauxAutoconsommationGlobale(0.0);
                        unite.setTauxAutoconsommationNette(0.0);
                        unite.setTauxGazTorche(0.0);
                    }
                    
                    // Objectif et écart (à adapter selon vos critères)
                    unite.setTauxObjectif(5.0); // Objectif standard de 5%
                    unite.setEcartObjectif(unite.getTauxAutoconsommationNette() - unite.getTauxObjectif());
                    
                    unites.add(unite);
                }
            }
            
            System.out.println("Données d'autoconsommation récupérées pour " + unites.size() + " unités");
            return unites;
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération de l'autoconsommation par unité: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Récupère les données de gaz torché par cause
     */
    public List<GazTorcheCauseDto> getGazTorcheParCause(LocalDate pmois) {
        try {
            System.out.println("=== DONNEES GAZ TORCHE PAR CAUSE ===");

            List<GazTorcheeParCauseTorchage> gazTorche = gazTorcheeRepository.findByPmois(pmois);
            List<GazTorcheCauseDto> causes = new ArrayList<>();

            // Calcul du total pour les pourcentages
            double totalGazTorche = gazTorche.stream()
                .mapToDouble(gt -> gt.getQuantiteGazTorchee() != null ? gt.getQuantiteGazTorchee().doubleValue() : 0.0)
                .sum();

            for (GazTorcheeParCauseTorchage gt : gazTorche) {
                GazTorcheCauseDto cause = new GazTorcheCauseDto();

                cause.setCause(gt.getLibCauseTorchage() != null ? gt.getLibCauseTorchage() : "Cause inconnue");
                cause.setType(mapperCodeCauseTorchage(gt.getCodeCauseTorchage()));
                cause.setQuantite(gt.getQuantiteGazTorchee() != null ? gt.getQuantiteGazTorchee().doubleValue() / 1000 : 0.0); // Division par 1000 pour affichage

                if (totalGazTorche > 0) {
                    cause.setPourcentage((cause.getQuantite() * 1000 / totalGazTorche) * 100); // Remultiplier par 1000 pour le calcul
                } else {
                    cause.setPourcentage(0.0);
                }

                causes.add(cause);
            }
            
            // Tri par quantité décroissante
            causes.sort((a, b) -> Double.compare(b.getQuantite(), a.getQuantite()));
            
            System.out.println("Données de gaz torché par cause récupérées: " + causes.size() + " causes");
            return causes;
            
        } catch (Exception e) {
            System.err.println("Erreur lors de la récupération du gaz torché par cause: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Récupère les totaux d'autoconsommation
     */
    public TotauxAutoconsommationDto getTotauxAutoconsommation(LocalDate pmois) {
        try {
            System.out.println("=== CALCUL TOTAUX AUTOCONSOMMATION ===");
            
            List<AutoconsommationUniteDto> unites = getAutoconsommationParUnite(pmois);
            TotauxAutoconsommationDto totaux = new TotauxAutoconsommationDto();
            
            double totalGnRecu = 0.0;
            double totalAutoconsommationGlobale = 0.0;
            double totalAutoconsommationNette = 0.0;
            double totalGazTorche = 0.0;
            double totalGazTorcheInterne = 0.0;
            double totalGazTorcheExterne = 0.0;
            
            for (AutoconsommationUniteDto unite : unites) {
                totalGnRecu += unite.getGnRecu() != null ? unite.getGnRecu() : 0.0;
                totalAutoconsommationGlobale += unite.getAutoconsommationGlobale() != null ? unite.getAutoconsommationGlobale() : 0.0;
                totalAutoconsommationNette += unite.getAutoconsommationNette() != null ? unite.getAutoconsommationNette() : 0.0;
                totalGazTorche += unite.getGazTorcheTotal() != null ? unite.getGazTorcheTotal() : 0.0;
                totalGazTorcheInterne += unite.getGazTorcheInterne() != null ? unite.getGazTorcheInterne() : 0.0;
                totalGazTorcheExterne += unite.getGazTorcheExterne() != null ? unite.getGazTorcheExterne() : 0.0;
            }
            
            totaux.setTotalGnRecu(totalGnRecu);
            totaux.setTotalAutoconsommationGlobale(totalAutoconsommationGlobale);
            totaux.setTotalAutoconsommationNette(totalAutoconsommationNette);
            totaux.setTotalGazTorche(totalGazTorche);
            totaux.setTotalGazTorcheInterne(totalGazTorcheInterne);
            totaux.setTotalGazTorcheExterne(totalGazTorcheExterne);
            
            // Calculs des taux globaux
            if (totalGnRecu > 0) {
                totaux.setTauxGlobalAutoconsommationGlobale((totalAutoconsommationGlobale / totalGnRecu) * 100);
                totaux.setTauxGlobalAutoconsommationNette((totalAutoconsommationNette / totalGnRecu) * 100);
                totaux.setTauxGlobalGazTorche((totalGazTorche / totalGnRecu) * 100);
            } else {
                totaux.setTauxGlobalAutoconsommationGlobale(0.0);
                totaux.setTauxGlobalAutoconsommationNette(0.0);
                totaux.setTauxGlobalGazTorche(0.0);
            }
            
            // Pourcentages gaz torché interne/externe
            if (totalGazTorche > 0) {
                totaux.setPourcentageGazTorcheInterne((totalGazTorcheInterne / totalGazTorche) * 100);
                totaux.setPourcentageGazTorcheExterne((totalGazTorcheExterne / totalGazTorche) * 100);
            } else {
                totaux.setPourcentageGazTorcheInterne(0.0);
                totaux.setPourcentageGazTorcheExterne(0.0);
            }
            
            System.out.println("Totaux AC calculés - AC Nette: " + totalAutoconsommationNette + 
                             ", Gaz Torché: " + totalGazTorche + 
                             ", Taux AC Nette: " + totaux.getTauxGlobalAutoconsommationNette() + "%");
            
            return totaux;

        } catch (Exception e) {
            System.err.println("Erreur lors du calcul des totaux d'autoconsommation: " + e.getMessage());
            e.printStackTrace();
            return new TotauxAutoconsommationDto();
        }
    }

    /**
     * Récupère l'évolution mensuelle de l'autoconsommation
     */
    public List<EvolutionAutoconsommationDto> getEvolutionAutoconsommation(LocalDate pmois, Integer nombreMois) {
        try {
            System.out.println("=== EVOLUTION AUTOCONSOMMATION ===");
            System.out.println("Période de fin: " + pmois + ", Nombre de mois: " + nombreMois);

            List<EvolutionAutoconsommationDto> evolution = new ArrayList<>();

            // Génération de données d'évolution (à remplacer par de vraies données)
            for (int i = nombreMois - 1; i >= 0; i--) {
                LocalDate moisCourant = pmois.minusMonths(i);
                String moisStr = moisCourant.format(DateTimeFormatter.ofPattern("MMM yyyy"));

                // Simulation de données - à remplacer par de vraies requêtes
                double autoconsommationNette = 4000 + (Math.random() * 2000); // Entre 4k et 6k
                double gazTorche = 1000 + (Math.random() * 1500);             // Entre 1k et 2.5k
                double tauxAutoconsommationNette = 4.5 + (Math.random() * 3.0); // Entre 4.5% et 7.5%
                double tauxGazTorche = 1.0 + (Math.random() * 2.0);            // Entre 1% et 3%

                evolution.add(new EvolutionAutoconsommationDto(moisStr, autoconsommationNette, gazTorche,
                                                             tauxAutoconsommationNette, tauxGazTorche));
            }

            System.out.println("Évolution autoconsommation générée pour " + evolution.size() + " mois");
            return evolution;

        } catch (Exception e) {
            System.err.println("Erreur lors de la génération de l'évolution autoconsommation: " + e.getMessage());
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * Détermine le statut d'une unité pour l'autoconsommation
     */
    private String determinerStatutUniteAC(AutoConsMens ac) {
        // Logique simplifiée - à adapter selon vos critères
        if (ac.getReceptionGnMois() != null && ac.getReceptionGnMois().doubleValue() > 0) {
            Double tauxAC = 0.0;
            if (ac.getReceptionGnMois().doubleValue() > 0 && ac.getAutoConsMoisNet() != null) {
                tauxAC = (ac.getAutoConsMoisNet().doubleValue() / ac.getReceptionGnMois().doubleValue()) * 100;
            }

            if (tauxAC <= 5.0) {
                return "ACCEPTABLE";
            } else if (tauxAC <= 8.0) {
                return "A_SURVEILLER";
            } else {
                return "CRITIQUE";
            }
        } else {
            return "ARRET_TOTAL";
        }
    }

    /**
     * Mappe le code cause torchage vers le type (Interne/Externe)
     */
    private String mapperCodeCauseTorchage(String codeCause) {
        if (codeCause == null) {
            return "INCONNU";
        }

        switch (codeCause.toUpperCase()) {
            case "I":
                return "INTERNE";
            case "E":
                return "EXTERNE";
            default:
                return "INCONNU";
        }
    }
}
