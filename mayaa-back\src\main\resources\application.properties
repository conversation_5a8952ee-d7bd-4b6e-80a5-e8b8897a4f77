spring.datasource.hikari.connection-test-query=SELECT 1 FROM DUAL
spring.datasource.hikari.minimum-idle=1
spring.profiles.active=@spring.profiles.active@

#############Database21C#############
spring.sql.init.platform=oracle
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#############Database#############

########################JPA##########################
#spring.jpa.generate-ddl=true
#spring.jpa.hibernate.ddl-auto = create
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.hibernate.ddl-auto=none
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.time-zone=UTC
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
########################JPA##########################


#################SENTRY#######################
sentry.traces-sample-rate=0.35
logging.level.p6spy=OFF
logging.level.com.p6spy=OFF
sentry.dsn=https://<EMAIL>:8443/9
sentry.send-default-pii=true
#################SENTRY#######################

##########Prometheus##############
management.endpoints.web.exposure.include=prometheus
##########Prometheus##############
spring.servlet.multipart.max-file-size=5MB
##########Keycloak##############
#spring.security.oauth2.resourceserver.jwt.issuer-uri=${keycloak.url}/realms/${keycloak.realm}
#spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
#keycloak.clientId=rokhssa-app
#keycloak.adminClientId=rokhssa-admin