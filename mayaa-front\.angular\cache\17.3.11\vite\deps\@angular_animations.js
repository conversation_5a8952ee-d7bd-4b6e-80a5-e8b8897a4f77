import {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationGroupPlayer,
  AnimationMetadataType,
  BrowserAnimationBuilder,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  ɵPRE_STYLE
} from "./chunk-AVAQI2KR.js";
import "./chunk-AXKGNXHS.js";
import "./chunk-RM24KKYX.js";
import "./chunk-PGF5GHY3.js";
import "./chunk-RZFGSV6T.js";
import "./chunk-JFE6O76F.js";
import "./chunk-J4B6MK7R.js";
export {
  AUTO_STYLE,
  AnimationBuilder,
  AnimationFactory,
  AnimationMetadataType,
  NoopAnimationPlayer,
  animate,
  animateChild,
  animation,
  group,
  keyframes,
  query,
  sequence,
  stagger,
  state,
  style,
  transition,
  trigger,
  useAnimation,
  AnimationGroupPlayer as ɵAnimationGroupPlayer,
  BrowserAnimationBuilder as ɵBrowserAnimationBuilder,
  ɵPRE_STYLE
};
//# sourceMappingURL=@angular_animations.js.map
