{"version": 3, "sources": ["../../../../../node_modules/primeng/fesm2022/primeng-icons-angleright.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nclass AngleRightIcon extends BaseIcon {\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵAngleRightIcon_BaseFactory;\n    return function AngleRightIcon_Factory(t) {\n      return (ɵAngleRightIcon_BaseFactory || (ɵAngleRightIcon_BaseFactory = i0.ɵɵgetInheritedFactory(AngleRightIcon)))(t || AngleRightIcon);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: AngleRightIcon,\n    selectors: [[\"AngleRightIcon\"]],\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 5,\n    consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M5.25 11.1728C5.14929 11.1694 5.05033 11.1455 4.9592 11.1025C4.86806 11.0595 4.78666 10.9984 4.72 10.9228C4.57955 10.7822 4.50066 10.5916 4.50066 10.3928C4.50066 10.1941 4.57955 10.0035 4.72 9.86283L7.72 6.86283L4.72 3.86283C4.66067 3.71882 4.64765 3.55991 4.68275 3.40816C4.71785 3.25642 4.79932 3.11936 4.91585 3.01602C5.03238 2.91268 5.17819 2.84819 5.33305 2.83149C5.4879 2.81479 5.64411 2.84671 5.78 2.92283L9.28 6.42283C9.42045 6.56346 9.49934 6.75408 9.49934 6.95283C9.49934 7.15158 9.42045 7.34221 9.28 7.48283L5.78 10.9228C5.71333 10.9984 5.63193 11.0595 5.5408 11.1025C5.44966 11.1455 5.35071 11.1694 5.25 11.1728Z\", \"fill\", \"currentColor\"]],\n    template: function AngleRightIcon_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(0, \"svg\", 0);\n        i0.ɵɵelement(1, \"path\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.getClassNames());\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n      }\n    },\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngleRightIcon, [{\n    type: Component,\n    args: [{\n      selector: 'AngleRightIcon',\n      standalone: true,\n      imports: [BaseIcon],\n      template: `\n        <svg width=\"14\" height=\"14\" viewBox=\"0 0 14 14\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" [attr.aria-label]=\"ariaLabel\" [attr.aria-hidden]=\"ariaHidden\" [attr.role]=\"role\" [class]=\"getClassNames()\">\n            <path\n                d=\"M5.25 11.1728C5.14929 11.1694 5.05033 11.1455 4.9592 11.1025C4.86806 11.0595 4.78666 10.9984 4.72 10.9228C4.57955 10.7822 4.50066 10.5916 4.50066 10.3928C4.50066 10.1941 4.57955 10.0035 4.72 9.86283L7.72 6.86283L4.72 3.86283C4.66067 3.71882 4.64765 3.55991 4.68275 3.40816C4.71785 3.25642 4.79932 3.11936 4.91585 3.01602C5.03238 2.91268 5.17819 2.84819 5.33305 2.83149C5.4879 2.81479 5.64411 2.84671 5.78 2.92283L9.28 6.42283C9.42045 6.56346 9.49934 6.75408 9.49934 6.95283C9.49934 7.15158 9.42045 7.34221 9.28 7.48283L5.78 10.9228C5.71333 10.9984 5.63193 11.0595 5.5408 11.1025C5.44966 11.1455 5.35071 11.1694 5.25 11.1728Z\"\n                fill=\"currentColor\"\n            />\n        </svg>\n    `\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngleRightIcon };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAGA,IAAM,iBAAN,MAAM,wBAAuB,SAAS;AAAA,EACpC,OAAO,OAAuB,uBAAM;AAClC,QAAI;AACJ,WAAO,SAAS,uBAAuB,GAAG;AACxC,cAAQ,gCAAgC,8BAAiC,sBAAsB,eAAc,IAAI,KAAK,eAAc;AAAA,IACtI;AAAA,EACF,GAAG;AAAA,EACH,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,IAC9B,YAAY;AAAA,IACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,IAChE,OAAO;AAAA,IACP,MAAM;AAAA,IACN,QAAQ,CAAC,CAAC,SAAS,MAAM,UAAU,MAAM,WAAW,aAAa,QAAQ,QAAQ,SAAS,4BAA4B,GAAG,CAAC,KAAK,onBAAonB,QAAQ,cAAc,CAAC;AAAA,IAC1wB,UAAU,SAAS,wBAAwB,IAAI,KAAK;AAClD,UAAI,KAAK,GAAG;AACV,QAAG,eAAe;AAClB,QAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,QAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,QAAG,aAAa;AAAA,MAClB;AACA,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,IAAI,cAAc,CAAC;AACjC,QAAG,YAAY,cAAc,IAAI,SAAS,EAAE,eAAe,IAAI,UAAU,EAAE,QAAQ,IAAI,IAAI;AAAA,MAC7F;AAAA,IACF;AAAA,IACA,eAAe;AAAA,EACjB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,QAAQ;AAAA,MAClB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}