import { Component, OnInit } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';
import { AppConfig, LayoutService } from './layout/service/app.layout.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  constructor(private primengConfig: PrimeNGConfig, private layoutService: LayoutService) { }

    ngOnInit(): void {
        this.primengConfig.ripple = true;       //enables core ripple functionality

        //optional configuration with the default configuration
        const config: AppConfig = {
          ripple: false, // toggles ripple on and off
          inputStyle: 'outlined', // default style for input elements
          menuMode: 'static', // layout mode of the menu, valid values are "static", "overlay", "slim", "horizontal", "drawer" and "reveal"
          colorScheme: 'light', // color scheme of the template, valid values are "light" and "dark"
          componentTheme: 'indigo', // default component theme for PrimeNG
          scale: 15, // size of the body font size to scale the whole application
          menuTheme: 'teal', // theme of the menu
          topbarTheme: 'teal', // theme of the topbar
          menuProfilePosition: 'end', // position of the profile menu inside the main menu, valid values are "start" and "end"
          topBarConfig: {
            // logoDark : "assets/layout/images/logo-sh/logo_white.png",
            appName: 'مَيْعَة',
            switchLightDark: true,
            searchBar: false,
            notifications: true,
            notificationsList: [
              {
                title: 'Demande accordée',
                details: 'Votre Demande N°2454 a été accordée !',
                icon: 'pi-verified',
                time: new Date(),
                routerLink: [],
              },
              {
                title: 'Demande refusée',
                details: 'Votre Demande N°2454 a été refusée !',
                icon: 'pi-times-circle',
                time: new Date(),
                onclick: () => {},
              },
            ],
            showApps: false,
            appsList: [
              {
                name: 'GESSOR',
                icon: 'pi-users',
                bgcolor: 'success',
                routerLink: [],
              },
              {
                name: 'GATIOR',
                icon: 'pi-cog',
                bgcolor: 'warning',
                onclick: () => {},
              },
              {
                name: 'RELEX',
                icon: 'pi-globe',
                bgcolor: 'help',
                onclick: () => {},
              },
            ],
            userMenu: [
              {
                label: 'Votre profile',
                icon: 'pi-user-edit',
                routerLink: [],
              },
              {
                label: 'A propos ?',
                icon: 'pi-question-circle',
                routerLink: [],
              },
            ],
            onDisconnect: () => {},
            showRightMneu: false,
            rightMenuComponent: undefined,
          },
        };
        this.layoutService.initAppConfig(config);
    }
}
