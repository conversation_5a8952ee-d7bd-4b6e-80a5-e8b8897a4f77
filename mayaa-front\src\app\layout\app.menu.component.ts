import { OnInit } from '@angular/core';
import { Component } from '@angular/core';

@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent implements OnInit {

    model: any[] = [];

    ngOnInit() {
        this.model = [
          {
            label: 'Mayaa - Unite',
            icon: 'pi pi-home',
            items: [
              {
                label: 'Bilan Global',
                icon: 'pi pi-fw pi-home',
                routerLink: ['/unite/bilan'],
              },
              {
                label: 'Performance',
                icon: 'pi pi-fw pi-chart-pie',
                routerLink: ['/unite/performance'],
              },
              {
                label: "Analyse de l'excès",
                icon: 'pi pi-fw pi-chart-pie',
                items: [
                  {
                    label: 'Energie interne et fuites',
                    icon: 'pi pi-fw pi-image',
                    routerLink: ['/unite/analyse-exces'],
                  },
                  {
                    label: 'Gaz Torché',
                    icon: 'pi pi-fw pi-list',
                    routerLink: ['/unite/analyse-exces-gt'],
                  },
                ],
              },
              {
                label: 'Evolution des causes',
                icon: 'pi pi-fw pi-chart-pie',
                routerLink: ['/unite/evolution-causes'],
              },
              {
                label: 'Les Arrêts',
                icon: 'pi pi-fw pi-stop-circle',
                items: [
                  {
                    label: 'Synthèse des arrêts',
                    icon: 'pi pi-fw pi-list',
                    routerLink: ['/unite/arrets/synthese'],
                  },

                  {
                    label: 'Répartition par siege',
                    icon: 'pi pi-fw pi-chart-bar',
                    routerLink: ['/unite/arrets/repartition-siege'],
                  },
                  {
                    label: 'Répartition par cause',
                    icon: 'pi pi-fw pi-chart-bar',
                    routerLink: ['/unite/arrets/repartition-cause'],
                  },
                  {
                    label: 'Situation des trains',
                    icon: 'pi pi-fw pi-cog',
                    routerLink: ['/unite/arrets/situation-trains'],
                  },
                ],
              },
              {
                label: 'Suivi des actions',
                icon: 'pi pi-fw pi-chart-pie',
                items: [
                  {
                    label: 'Commentaires Mensuelles',
                    icon: 'pi pi-fw pi-calendar',
                    routerLink: ['/unite/suivi-action-comm'],
                  },
                  {
                    label: 'Suivi des actions a entreprendre',
                    icon: 'pi pi-fw pi-calendar',
                    routerLink: ['/unite/suivi-action-entrep'],
                  },
                  {
                    label: 'Finalisé - Etat Mensuel',
                    icon: 'pi pi-fw pi-calendar',
                    routerLink: ['/unite/suivi-action-final'],
                  },
                ],
              },
              {
                label: 'Rapport Unité',
                icon: 'pi pi-fw pi-eye',
                routerLink: ['/unite/reporting'],
              },
            ],
          },
          {
            label: 'Mayaa - Consolidé',
            icon: 'pi pi-fw pi-star-fill',
            items: [
              {
                label: 'Dashboard',
                icon: 'pi pi-fw pi-id-card',
                items: [
                  {
                    label: 'Réalisation',
                    icon: 'pi pi-fw pi-chart-line',
                    routerLink: ['/conso/realisation'],
                  },
                  {
                    label: 'Autoconsommation',
                    icon: 'pi pi-fw pi-chart-pie',
                    routerLink: ['/conso/autoconsommation'],
                  },
                  {
                    label: 'Les Arrêts',
                    icon: 'pi pi-fw pi-stop-circle',
                    routerLink: ['/conso/arrets'],
                  },
                ],
              },
              {
                label: 'Validation',
                icon: 'pi pi-fw pi-check-square',
                routerLink: ['/uikit/input'],
              },
              {
                label: 'Rapport Consolidé',
                icon: 'pi pi-fw pi-globe',
                url: ['https://www.primefaces.org/primeblocks-ng'],
                target: '_blank',
              },
            ],
          },
        ];
    }
}
