import { Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { LayoutService, TopBarConfig } from './service/app.layout.service';
import { MayaaUniteService } from '../modules/mayaa-unite/services/mayaa-unite.service';
import { TestDataComplexe } from '../model/TestDataComplexe';
import { CalendarService } from '../services/calendar.service';
import { ListUnite } from '../model/ListUnite';
@Component({
  selector: 'app-topbar',
  styleUrl: './app.topbar.component.scss',
  templateUrl: './app.topbar.component.html',
})
export class AppTopbarComponent implements OnInit {
  @ViewChild('menuButton') menuButton!: ElementRef;

  @ViewChild('mobileMenuButton') mobileMenuButton!: ElementRef;

  @ViewChild('searchInput') searchInput!: ElementRef;

  @Input() topBarConfig!: TopBarConfig;
  activeItem!: number;
  selectedMonthYear: Date | null = null;
  uniteList: { label: string; value: string }[] = [];
  dataStatus: 'upToDate' | 'changed' | 'noData' = 'noData';
  selectedUnite: string;

  diffDialogVisible = false;
  dataRows: any[] = [];
  hasDifference: boolean = false;

  constructor(
    public layoutService: LayoutService,
    public el: ElementRef,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private mayaaUniteService: MayaaUniteService,
    private calendarService: CalendarService
  ) {
    this.selectedUnite = '5X3';
    this.calendarService.setSelectedUnite(this.selectedUnite);
  }



  /**
   * Charge la liste des unités depuis la vue LIST_UNITE
   */
  private loadUnitesList(): void {
    this.mayaaUniteService.getListUnites().subscribe({
      next: (unites: ListUnite[]) => {
        // Créer un Set pour éviter les doublons de codes d'unité
        const uniqueUnites = new Map<string, { label: string; value: string }>();

        unites.forEach(unite => {
          if (!uniqueUnites.has(unite.codeUnite)) {
            uniqueUnites.set(unite.codeUnite, {
              label: unite.unite, // Utilise le nom de l'unité (ex: "GL1Z")
              value: unite.codeUnite // Utilise le code de l'unité (ex: "5X2")
            });
          }
        });

        // Convertir en tableau et trier par label
        this.uniteList = Array.from(uniqueUnites.values())
          .sort((a, b) => a.label.localeCompare(b.label));

        // Si l'unité sélectionnée n'existe pas dans la nouvelle liste, prendre la première
        if (this.uniteList.length > 0 &&
            !this.uniteList.some(u => u.value === this.selectedUnite)) {
          this.selectedUnite = this.uniteList[0].value;
          this.calendarService.setSelectedUnite(this.selectedUnite);
        }
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la liste des unités:', error);
        // En cas d'erreur, utiliser la liste par défaut
        this.uniteList = [
          { label: 'GL1Z', value: '5X2' },
          { label: 'GL2Z', value: '5X3' },
          { label: 'GL1K', value: '5X1' },
          { label: 'GL3Z', value: '5X8' },
        ];
      }
    });
  }

  /**
   * Méthode publique pour rafraîchir la liste des unités
   * Utile si les données de LIST_UNITE sont mises à jour
   */
  public refreshUnitesList(): void {
    this.loadUnitesList();
  }

  onMonthYearChange(date: Date): void {
    this.selectedMonthYear = date;
    this.calendarService.setSelectedMonthYear(this.selectedMonthYear);
    this.verifyDataForCurrentSelection();
  }

  onUniteChange(unite: string): void {
    this.selectedUnite = unite;
    this.calendarService.setSelectedUnite(this.selectedUnite);
    this.verifyDataForCurrentSelection();
  }

  private verifyDataForCurrentSelection(): void {
    if (!this.selectedMonthYear) return;

    const month: string = (this.selectedMonthYear.getMonth() + 1)
      .toString()
      .padStart(2, '0');
    const year: string = this.selectedMonthYear.getFullYear().toString();

    this.mayaaUniteService
      .verifDataComplexe(month, year, this.selectedUnite)
      .subscribe(
        (data) => {
          // Si des données sont trouvées, on met à jour le tableau et le status
          this.dataRows = [
            {
              field: 'Autoconsommation',
              loaded: data.tauxAutoConsMoisGlobal,
              sispro: data.tauxAutoConsMoisGlobalComplexe,
            },
            {
              field: 'Gaz Torchée',
              loaded: data.gazTorcheeMois,
              sispro: data.gazTorcheeMoisComplexe,
            },
          ];
          this.hasDifference = this.dataRows.some(
            (row) => row.loaded !== row.sispro
          );
          this.dataStatus = this.hasDifference ? 'changed' : 'upToDate';
        },
        (error) => {
          if (error.status === 404) {
            // Si 404 : aucune donnée trouvée pour ce mois, on propose de charger ce mois
            this.dataStatus = 'noData';
            this.confirmRefresh(this.selectedMonthYear!, 2);
          } else {
            console.error(
              'Erreur lors de la vérification des données :',
              error
            );
          }
        }
      );
  }
  verifData() {
    this.mayaaUniteService
      .getLastPMois(this.selectedUnite)
      .subscribe((date: Date) => {
        this.selectedMonthYear = date;
        this.calendarService.setSelectedMonthYear(this.selectedMonthYear);
        const month: string = (date.getMonth() + 1).toString().padStart(2, '0');
        const year: string = date.getFullYear().toString();
        this.mayaaUniteService
          .verifDataComplexe(month, year, this.selectedUnite)
          .subscribe((data) => {
            this.dataRows = [
              {
                field: 'Autoconsommation',
                loaded: data.tauxAutoConsMoisGlobal,
                sispro: data.tauxAutoConsMoisGlobalComplexe,
              },
              {
                field: 'Gaz Torchée',
                loaded: data.gazTorcheeMois,
                sispro: data.gazTorcheeMoisComplexe,
              },
            ];
            this.hasDifference = this.dataRows.some(
              (row) => row.loaded !== row.sispro
            );
            if (this.hasDifference) {
              this.dataStatus = 'changed';
            } else {
              this.dataStatus = 'upToDate';
            }
          });
      });
  }
  ngOnInit(): void {
    this.loadUnitesList();
    this.verifData();
  }
  showDiffDialog() {
    if (this.dataStatus !== 'upToDate') {
      this.diffDialogVisible = true;
    }
  }

  confirmRefresh(date: Date, type: number): void {
    const month: string = (date.getMonth() + 1).toString().padStart(2, '0');
    const year: string = date.getFullYear().toString();
    let periode = `${month}/${year}`;
    let message =
      'Etes vous sûr de vouloir actualiser les données depuis SISPRO ?';
    let headerText = 'Actualisation';
    if (type == 2) {
      message = `Voulez vous charger La periode ${periode} ?`;
      headerText = 'Donnée manquants';
    }
    this.confirmationService.confirm({
      message: message,
      header: headerText,
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Oui',
      rejectLabel: 'Non',
      rejectButtonStyleClass: 'p-button-text',
      accept: () => {
        if (this.selectedMonthYear) {
          const month: string = (this.selectedMonthYear.getMonth() + 1)
            .toString()
            .padStart(2, '0');
          const year: string = this.selectedMonthYear.getFullYear().toString();
          this.mayaaUniteService
            .loadAutoCons(month, year, this.selectedUnite)
            .subscribe({
              next: (v) => {
                this.verifData();
                this.diffDialogVisible = false;
                this.messageService.add({
                  severity: 'success',
                  summary: 'Confirmed',
                  detail: 'Donnée mis à jour !',
                });
              },
              error: (e) => {
                console.log(e);
              },
            });
        }
      },
    });
  }

  get mobileTopbarActive(): boolean {
    return this.layoutService.state.topbarMenuActive;
  }

  toggleDarkTheme() {
    this.layoutService.config.update((config) => ({
      ...config,
      menuTheme:
        this.layoutService.config().colorScheme == 'light'
          ? 'dark'
          : config.menuTheme,
      colorScheme:
        this.layoutService.config().colorScheme == 'light' ? 'dark' : 'light',
    }));
    localStorage.setItem('theme', this.layoutService.config().colorScheme);
  }

  onMenuButtonClick() {
    this.layoutService.onMenuToggle();
  }

  onRightMenuButtonClick() {
    this.layoutService.openRightSidebar();
  }

  onMobileTopbarMenuButtonClick() {
    this.layoutService.onTopbarMenuToggle();
  }

  focusSearchInput() {
    setTimeout(() => {
      this.searchInput.nativeElement.focus();
    }, 0);
  }
}
