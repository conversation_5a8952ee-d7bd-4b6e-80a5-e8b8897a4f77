/**
 * Interface pour les données d'autoconsommation d'une unité
 */
export interface AutoconsommationUnite {
  /** Code de l'unité */
  uniteCode: string;

  /** Nom de l'unité */
  unite: string;

  /** Statut de l'unité */
  statut: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';

  /** GN reçu (en 10³ m³) */
  gnRecu: number;

  /** Autoconsommation globale (en 10³ m³) */
  autoconsommationGlobale: number;

  /** Autoconsommation nette (en 10³ m³) */
  autoconsommationNette: number;

  /** Gaz torché total (en 10³ m³) */
  gazTorcheTotal: number;

  /** Gaz torché interne (en 10³ m³) */
  gazTorcheInterne: number;

  /** Gaz torché externe (en 10³ m³) */
  gazTorcheExterne: number;

  /** Taux autoconsommation globale (%) */
  tauxAutoconsommationGlobale: number;

  /** Taux autoconsommation nette (%) */
  tauxAutoconsommationNette: number;

  /** Taux gaz torché (%) */
  tauxGazTorche: number;

  /** Taux objectif autoconsommation (%) */
  tauxObjectif: number;

  /** Écart par rapport à l'objectif (%) */
  ecartObjectif: number;
}

/**
 * Interface pour les données de gaz torché par cause
 */
export interface GazTorcheCause {
  /** Cause du torchage */
  cause: string;

  /** Type de cause (Interne/Externe) */
  type: 'INTERNE' | 'EXTERNE';

  /** Quantité (en 10³ m³) */
  quantite: number;

  /** Pourcentage du total (%) */
  pourcentage: number;
}

/**
 * Interface pour les totaux d'autoconsommation consolidés
 */
export interface TotauxAutoconsommation {
  /** Total GN reçu (en 10³ m³) */
  totalGnRecu: number;

  /** Total autoconsommation globale (en 10³ m³) */
  totalAutoconsommationGlobale: number;

  /** Total autoconsommation nette (en 10³ m³) */
  totalAutoconsommationNette: number;

  /** Total gaz torché (en 10³ m³) */
  totalGazTorche: number;

  /** Total gaz torché interne (en 10³ m³) */
  totalGazTorcheInterne: number;

  /** Total gaz torché externe (en 10³ m³) */
  totalGazTorcheExterne: number;

  /** Taux global autoconsommation globale (%) */
  tauxGlobalAutoconsommationGlobale: number;

  /** Taux global autoconsommation nette (%) */
  tauxGlobalAutoconsommationNette: number;

  /** Taux global gaz torché (%) */
  tauxGlobalGazTorche: number;

  /** Pourcentage gaz torché interne (%) */
  pourcentageGazTorcheInterne: number;

  /** Pourcentage gaz torché externe (%) */
  pourcentageGazTorcheExterne: number;
}

/**
 * Interface pour l'évolution mensuelle de l'autoconsommation
 */
export interface EvolutionAutoconsommation {
  /** Mois (format YYYY-MM ou libellé) */
  mois: string;

  /** Autoconsommation nette (en 10³ m³) */
  autoconsommationNette: number;

  /** Gaz torché (en 10³ m³) */
  gazTorche: number;

  /** Taux autoconsommation nette (%) */
  tauxAutoconsommationNette: number;

  /** Taux gaz torché (%) */
  tauxGazTorche: number;
}

/**
 * Interface pour les statistiques d'autoconsommation
 */
export interface StatistiquesAutoconsommation {
  /** Nombre d'unités avec AC acceptable (≤8%) */
  nombreUnitesAcceptables: number;

  /** Nombre d'unités avec AC à surveiller (8-12%) */
  nombreUnitesASurveiller: number;

  /** Nombre d'unités avec AC critique (>12%) */
  nombreUnitesCritiques: number;

  /** Performance globale */
  performanceGlobale: 'ACCEPTABLE' | 'A_SURVEILLER' | 'CRITIQUE';
}

/**
 * Interface principale pour les données d'autoconsommation
 */
export interface DonneesAutoconsommation {
  /** Période sélectionnée (format YYYYMM) */
  periode: string;

  /** Date de dernière mise à jour */
  derniereMiseAJour: Date;

  /** Statistiques d'autoconsommation */
  statistiques: StatistiquesAutoconsommation;

  /** Données d'autoconsommation par unité */
  unites: AutoconsommationUnite[];

  /** Données de gaz torché par cause */
  gazTorcheCauses: GazTorcheCause[];

  /** Totaux consolidés */
  totaux: TotauxAutoconsommation;

  /** Évolution mensuelle (6 derniers mois) */
  evolution: EvolutionAutoconsommation[];
}

/**
 * Interface pour les données de graphique d'autoconsommation
 */
export interface ChartDataAutoconsommation {
  labels: string[];
  datasets: ChartDatasetAutoconsommation[];
}

/**
 * Interface pour un dataset de graphique d'autoconsommation
 */
export interface ChartDatasetAutoconsommation {
  label?: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  tension?: number;
  fill?: boolean;
  type?: string;
}

/**
 * Interface pour les options de graphique d'autoconsommation
 */
export interface ChartOptionsAutoconsommation {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      position?: 'top' | 'bottom' | 'left' | 'right';
      labels?: {
        usePointStyle?: boolean;
        font?: {
          size?: number;
        };
      };
    };
    tooltip?: {
      callbacks?: {
        label?: (context: any) => string;
      };
    };
  };
  scales?: {
    x?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
    y?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
      beginAtZero?: boolean;
      ticks?: {
        callback?: (value: any) => string;
      };
    };
  };
}
