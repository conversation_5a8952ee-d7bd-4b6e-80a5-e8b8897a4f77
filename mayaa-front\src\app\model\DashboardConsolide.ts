import { RepartitionGnRecu, ProductionGnlConsolidee, TotalConsolide } from './RealisationUnite';

/**
 * Interface pour les données d'autoconsommation consolidées par unité
 */
export interface AutoconsommationConsolidee {
  /** Code de l'unité */
  uniteCode: string;

  /** Nom de l'unité */
  unite: string;

  /** Autoconsommation globale (en 10³ m³) */
  autoconsommationGlobale: number;

  /** Autoconsommation nette (en 10³ m³) */
  autoconsommationNette: number;

  /** Gaz torché total (en 10³ m³) */
  gazTorcheTotal: number;

  /** Gaz torché interne (en 10³ m³) */
  gazTorcheInterne: number;

  /** Gaz torché externe (en 10³ m³) */
  gazTorcheExterne: number;

  /** Taux autoconsommation globale (%) */
  tauxAutoconsommationGlobale: number;

  /** Taux autoconsommation nette (%) */
  tauxAutoconsommationNette: number;

  /** Taux gaz torché (%) */
  tauxGazTorche: number;

  /** Taux objectif autoconsommation */
  tauxObjectif: number;

  /** Taux design autoconsommation */
  tauxDesign: number;

  /** Écart par rapport à l'objectif */
  ecartObjectif: number;

  /** Écart par rapport au design */
  ecartDesign: number;

  /** Statut de l'unité */
  statut: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';
}

/**
 * Interface pour l'évolution mensuelle
 */
export interface EvolutionMensuelle {
  /** Mois (format YYYY-MM) */
  mois: string;

  /** Autoconsommation nette totale */
  autoconsommationNette: number;

  /** Gaz torché total */
  gazTorche: number;

  /** Taux autoconsommation nette global (%) */
  tauxAutoconsommationNette: number;

  /** Taux gaz torché global (%) */
  tauxGazTorche: number;
}

/**
 * Interface pour les totaux d'autoconsommation consolidés
 */
export interface TotalAutoconsommationConsolide {
  /** Total autoconsommation globale */
  totalAutoconsommationGlobale: number;

  /** Total autoconsommation nette */
  totalAutoconsommationNette: number;

  /** Total gaz torché */
  totalGazTorche: number;

  /** Total gaz torché interne */
  totalGazTorcheInterne: number;

  /** Total gaz torché externe */
  totalGazTorcheExterne: number;

  /** Taux global autoconsommation globale (%) */
  tauxGlobalAutoconsommationGlobale: number;

  /** Taux global autoconsommation nette (%) */
  tauxGlobalAutoconsommationNette: number;

  /** Taux global gaz torché (%) */
  tauxGlobalGazTorche: number;

  /** Pourcentage gaz torché interne */
  pourcentageGazTorcheInterne: number;

  /** Pourcentage gaz torché externe */
  pourcentageGazTorcheExterne: number;
}

/**
 * Interface principale pour les données du dashboard consolidé
 */
export interface DashboardConsolide {
  /** Période sélectionnée */
  periode: string;

  /** Date de dernière mise à jour */
  derniereMiseAJour: Date;

  /** Données de répartition du GN reçu par unité */
  repartitionGnRecu: RepartitionGnRecu[];

  /** Données de production GNL par unité */
  productionGnl: ProductionGnlConsolidee[];

  /** Données d'autoconsommation par unité */
  autoconsommation: AutoconsommationConsolidee[];

  /** Évolution mensuelle (6 derniers mois) */
  evolutionMensuelle: EvolutionMensuelle[];

  /** Totaux consolidés pour la réalisation */
  totauxRealisation: TotalConsolide;

  /** Totaux consolidés pour l'autoconsommation */
  totauxAutoconsommation: TotalAutoconsommationConsolide;

  /** Nombre d'unités actives */
  nombreUnitesActives: number;

  /** Nombre d'unités en arrêt total */
  nombreUnitesArretTotal: number;

  /** Nombre d'unités en maintenance */
  nombreUnitesMaintenance: number;
}

/**
 * Interface pour les données de graphique (compatible Chart.js)
 */
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

/**
 * Interface pour un dataset de graphique
 */
export interface ChartDataset {
  label?: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  tension?: number;
  fill?: boolean;
  type?: string;
}

/**
 * Interface pour les options de graphique (compatible Chart.js)
 */
export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      position?: 'top' | 'bottom' | 'left' | 'right';
      labels?: {
        usePointStyle?: boolean;
        font?: {
          size?: number;
        };
      };
    };
    tooltip?: {
      callbacks?: {
        label?: (context: any) => string;
      };
    };
  };
  scales?: {
    x?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
    y?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
      beginAtZero?: boolean;
      ticks?: {
        callback?: (value: any) => string;
      };
    };
  };
}
