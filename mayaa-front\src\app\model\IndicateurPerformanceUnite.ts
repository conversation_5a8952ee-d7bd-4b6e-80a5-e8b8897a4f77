export interface IndicateurPerformanceUnite {
  idAuto: number;
  unite: string;
  mois: string; // Format ISO date string
  production: number;
  productionDesign: number;
  trainMarche: number;
  tp: number;
  tf: number;
  tc: number;
  tap: number;
  tai: number;
}

export interface IndicateurPerformanceRow {
  type: 'design' | 'reelle';
  nombreTrains: number;
  tp: number;
  tf: number;
  tc: number;
  tap: number;
  tai: number;
  productionGNL: number;
  consED: number;
  tauxAppoint: number;
  consVapeur: number;
  consElect: number;
}

export interface IndicateurPerformanceTableData {
  design: IndicateurPerformanceRow;
  reelle: IndicateurPerformanceRow;
}
