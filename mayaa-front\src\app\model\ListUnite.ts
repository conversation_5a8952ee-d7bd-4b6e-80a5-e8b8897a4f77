/**
 * Interface représentant les données de la vue LIST_UNITE
 * Contient les informations sur les unités avec leurs objectifs et taux design d'autoconsommation
 */
export interface ListUnite {
  /** Identifiant unique */
  id: number;

  /** Code de l'unité (ex: "5X2", "5X3") */
  codeUnite: string;

  /** Nom de l'unité (ex: "GL1Z", "GL2Z") */
  unite: string;

  /** Période au format Date (ex: "2024-01-01") */
  pmois: string | Date;

  /** Taux objectif d'autoconsommation en pourcentage (ex: 19) */
  tauxAcObj: number;

  /** Taux design d'autoconsommation en pourcentage (ex: 14.8) */
  tauxAcDesign: number;
}
