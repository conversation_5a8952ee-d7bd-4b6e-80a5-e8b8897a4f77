/**
 * Interface pour les données de réalisation d'une unité
 */
export interface RealisationUnite {
  /** Code de l'unité */
  uniteCode: string;

  /** Nom de l'unité */
  unite: string;

  /** Statut de l'unité */
  statut: 'ACTIF' | 'ARRET_TOTAL' | 'MAINTENANCE';

  /** Production réelle (en tonnes) */
  productionReelle: number;

  /** Objectif de production (en tonnes) */
  objectifProduction: number;

  /** Prévision de production (en tonnes) */
  previsionProduction: number;

  /** Écart par rapport à l'objectif (%) */
  ecartObjectif: number;

  /** Taux de réalisation par rapport à l'objectif (%) */
  tauxRealisationObjectif: number;

  /** GN reçu (en 10³ m³) */
  gnRecu: number;

  /** GN transformé (en 10³ m³) */
  gnTransforme: number;

  /** Pourcentage de GN transformé (%) */
  pourcentageGnTransforme: number;
}

/**
 * Interface pour les totaux de réalisation consolidés
 */
export interface TotauxRealisation {
  /** Total GN reçu (en 10³ m³) */
  totalGnRecu: number;

  /** Total GN transformé (en 10³ m³) */
  totalGnTransforme: number;

  /** Pourcentage global de GN transformé (%) */
  pourcentageGlobalGnTransforme: number;

  /** Total production réelle (en tonnes) */
  totalProductionReelle: number;

  /** Total objectif (en tonnes) */
  totalObjectif: number;

  /** Total prévision (en tonnes) */
  totalPrevision: number;

  /** Taux global de réalisation par rapport à l'objectif (%) */
  tauxGlobalRealisationObjectif: number;
}

/**
 * Interface pour l'évolution mensuelle de la production
 */
export interface EvolutionProduction {
  /** Mois (format YYYY-MM ou libellé) */
  mois: string;

  /** Production réelle (en tonnes) */
  production: number;

  /** Objectif (en tonnes) */
  objectif: number;

  /** Taux de réalisation (%) */
  tauxRealisation: number;
}

/**
 * Interface pour les statistiques générales
 */
export interface StatistiquesGenerales {
  /** Nombre d'unités actives */
  nombreUnitesActives: number;

  /** Nombre d'unités en arrêt total */
  nombreUnitesArretTotal: number;

  /** Nombre d'unités en maintenance */
  nombreUnitesMaintenance: number;

  /** Nombre total d'unités */
  nombreTotalUnites: number;
}

/**
 * Interface principale pour les données de réalisation
 */
export interface DonneesRealisation {
  /** Période sélectionnée (format YYYYMM) */
  periode: string;

  /** Date de dernière mise à jour */
  derniereMiseAJour: Date;

  /** Statistiques générales */
  statistiques: StatistiquesGenerales;

  /** Données de réalisation par unité */
  unites: RealisationUnite[];

  /** Totaux consolidés */
  totaux: TotauxRealisation;

  /** Évolution mensuelle (6 derniers mois) */
  evolution: EvolutionProduction[];
}

/**
 * Interface pour les données de graphique (compatible Chart.js)
 */
export interface ChartDataRealisation {
  labels: string[];
  datasets: ChartDatasetRealisation[];
}

/**
 * Interface pour un dataset de graphique de réalisation
 */
export interface ChartDatasetRealisation {
  label?: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  tension?: number;
  fill?: boolean;
  type?: string;
}

/**
 * Interface pour les options de graphique de réalisation
 */
export interface ChartOptionsRealisation {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      position?: 'top' | 'bottom' | 'left' | 'right';
      labels?: {
        usePointStyle?: boolean;
        font?: {
          size?: number;
        };
      };
    };
    tooltip?: {
      callbacks?: {
        label?: (context: any) => string;
      };
    };
  };
  scales?: {
    x?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
    };
    y?: {
      display?: boolean;
      title?: {
        display?: boolean;
        text?: string;
      };
      beginAtZero?: boolean;
      ticks?: {
        callback?: (value: any) => string;
      };
    };
  };
}
