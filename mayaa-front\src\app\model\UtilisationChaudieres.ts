export interface UtilisationChaudieres {
  id: number;
  unite: string;
  typeChaudieres: string;
  mois: string; // Format ISO date
  capaciteInstallee: number; // T/H
  chaudieresUtiliseDesign: number; // Nombre de chaudières utilisées
  productionVapeur: number; // Production vapeur
  nombreArretVolontaire: number; // Nombre d'arrêt volontaire
  nombreArretDeclenchement: number; // Nombre d'arrêt déclenchement
  consommationFuelGaz: number; // Consommation Fuel gaz (eq GN) CM3
}

export interface ChaudieresTableRow {
  typeChaudieres: string;
  capaciteInstallee: number;
  chaudieresUtilises: number;
  nombreArretVolontaire: number;
  nombreArretDeclenchement: number;
  productionVapeur: number;
  consommationFuelGaz: number;
}

export interface ChaudieresTableData {
  rows: ChaudieresTableRow[];
  totalCapaciteInstallee: number;
  totalChaudieresUtilises: number;
  totalProductionVapeur: number;
  totalConsommationFuelGaz: number;
}
