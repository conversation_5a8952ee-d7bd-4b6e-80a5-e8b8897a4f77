<div class="grid">
  <!-- Statistiques globales en haut -->
  <div class="col-12" *ngIf="statistiquesGlobales">
    <div class="stats-overview">
      <div class="stat-card total">
        <div class="stat-value">{{ getTotalArrets() }}</div>
        <div class="stat-label">Total Arrêts</div>
      </div>
      <div class="stat-card heures">
        <div class="stat-value">{{ getTotalHeuresArret() }}</div>
        <div class="stat-label">Heures d'Arrêt</div>
      </div>
      <div class="stat-card map">
        <div class="stat-value">{{ formatNumber(getMoyenneMAP()) }}%</div>
        <div class="stat-label">MAP Moyenne</div>
      </div>
      <div class="stat-card unites">
        <div class="stat-value">{{ syntheseGlobale.length }}</div>
        <div class="stat-label">Unités</div>
      </div>
    </div>
  </div>

  <!-- Synthèse Globale -->
  <div class="col-12">
    <p-card header="Synthèse Globale des Arrêts" [style]="{'margin-bottom': '1rem'}">
      <p-table
        [value]="syntheseGlobale"
        [loading]="loading"
        [scrollable]="true"
        scrollHeight="400px"
        [rowHover]="true"
        styleClass="p-datatable-sm">

        <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of colsSynthese" [style.width]="col.width">
              {{ col.header }}
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rowData>
          <tr>
            <td><strong>{{ rowData.unite }}</strong></td>
            <td class="text-center">{{ rowData.totalArrets }}</td>
            <td class="text-center">{{ rowData.totalHeuresArret }}</td>
            <td class="text-center">{{ formatNumber(rowData.mapMoyenne) }}%</td>
            <td class="text-center">{{ formatNumber(rowData.acNetteMoyenne) }}%</td>
            <td class="text-center">{{ formatNumber(rowData.gtTotalInterne) }}</td>
            <td class="text-center">{{ formatNumber(rowData.gtTotalExterne) }}</td>
            <td>{{ rowData.causePrincipale }}</td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="colsSynthese.length" class="text-center">
              Aucune donnée disponible
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-card>
  </div>

  <!-- Répartition par Siège - Vue Consolidée -->
  <div class="col-12 lg:col-6">
    <p-card header="Répartition par Siège - Consolidé" [style]="{'margin-bottom': '1rem'}">
      <div class="chart-container">
        <p-chart
          type="pie"
          [data]="pieChartDataSiege"
          [options]="pieChartOptions"
          *ngIf="pieChartDataSiege">
        </p-chart>
      </div>

      <!-- Tableau détaillé -->
      <div class="mt-4">
        <p-table
          [value]="repartitionSiege"
          [loading]="loading"
          styleClass="p-datatable-sm">

          <ng-template pTemplate="header">
            <tr>
              <th>Siège</th>
              <th>Type</th>
              <th>Total</th>
              <th>%</th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-rowData>
            <tr>
              <td>{{ rowData.siegeCause }}</td>
              <td>
                <span [ngClass]="getTypeBadgeClass(rowData.typeSiege)" class="badge">
                  {{ rowData.typeSiege }}
                </span>
              </td>
              <td class="text-center">{{ rowData.totalQuantite }}</td>
              <td class="text-center">{{ formatNumber(rowData.pourcentageGlobal) }}%</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </p-card>
  </div>

  <!-- Répartition par Cause - Vue Consolidée -->
  <div class="col-12 lg:col-6">
    <p-card header="Répartition par Cause - Consolidé" [style]="{'margin-bottom': '1rem'}">
      <div class="chart-container">
        <p-chart
          type="pie"
          [data]="pieChartDataCause"
          [options]="pieChartOptions"
          *ngIf="pieChartDataCause">
        </p-chart>
      </div>

      <!-- Tableau détaillé -->
      <div class="mt-4">
        <p-table
          [value]="repartitionCause"
          [loading]="loading"
          styleClass="p-datatable-sm">

          <ng-template pTemplate="header">
            <tr>
              <th>Cause</th>
              <th>Type</th>
              <th>Total</th>
              <th>%</th>
            </tr>
          </ng-template>

          <ng-template pTemplate="body" let-rowData>
            <tr>
              <td>{{ rowData.cause }}</td>
              <td>
                <span [ngClass]="getTypeBadgeClass(rowData.typeCause)" class="badge">
                  {{ rowData.typeCause }}
                </span>
              </td>
              <td class="text-center">{{ rowData.totalQuantite }}</td>
              <td class="text-center">{{ formatNumber(rowData.pourcentageGlobal) }}%</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </p-card>
  </div>

  <!-- Situation des Trains - Vue Consolidée -->
  <div class="col-12">
    <p-card header="Situation des Trains - Consolidé">
      <p-table
        [value]="situationTrains"
        [loading]="loading"
        [scrollable]="true"
        scrollHeight="300px"
        [rowHover]="true"
        styleClass="p-datatable-sm">

        <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of colsSituation" [style.width]="col.width">
              {{ col.header }}
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rowData>
          <tr>
            <td>
              <span [ngClass]="getTypeBadgeClass(rowData.typeCause)" class="badge">
                {{ rowData.typeCause }}
              </span>
            </td>
            <td class="text-center"><strong>{{ rowData.totalArrets }}</strong></td>
            <td>{{ formatCausesList(rowData.causesPrincipalesGlobales) }}</td>
            <td>{{ rowData.analysesConsolidees }}</td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="colsSituation.length" class="text-center">
              Aucune donnée disponible
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-card>
  </div>

  <!-- Message de chargement -->
  <div class="col-12" *ngIf="loading">
    <p-card>
      <div class="text-center">
        <p-progressSpinner></p-progressSpinner>
        <p class="mt-3">Chargement des données consolidées...</p>
      </div>
    </p-card>
  </div>
</div>
