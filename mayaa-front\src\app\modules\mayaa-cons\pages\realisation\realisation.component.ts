import { Component, OnInit, OnD<PERSON>roy, effect } from '@angular/core';
import { Subscription } from 'rxjs';

import { RealisationService } from '../../services/realisation.service';
import { CalendarService } from '../../../../services/calendar.service';
import {
  DonneesRealisation,
  ChartDataRealisation,
  ChartOptionsRealisation
} from '../../../../model/Realisation';

@Component({
  selector: 'app-realisation',
  templateUrl: './realisation.component.html',
  styleUrl: './realisation.component.scss'
})
export class RealisationComponent implements OnInit, OnDestroy {
  donneesRealisation: DonneesRealisation | null = null;
  loading = true;
  error: string | null = null;

  // Données pour les graphiques de réalisation
  productionGnlChartData: ChartDataRealisation | null = null;
  productionGnlChartOptions: ChartOptionsRealisation | null = null;
  evolutionChartData: ChartDataRealisation | null = null;
  evolutionChartOptions: ChartOptionsRealisation | null = null;

  private subscription = new Subscription();

  constructor(
    private realisationService: RealisationService,
    private calendarService: CalendarService
  ) {
    // Écouter les changements de calendrier
    effect(() => {
      const selectedDate = this.calendarService.selectedMonthYear();
      if (selectedDate) {
        this.loadRealisationData();
      }
    });
  }

  ngOnInit() {
    this.loadRealisationData();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  /**
   * Charge les données de réalisation
   */
  private loadRealisationData() {
    this.loading = true;
    this.error = null;

    const selectedDate = this.calendarService.selectedMonthYear();
    if (!selectedDate) {
      this.error = 'Aucune date sélectionnée';
      this.loading = false;
      return;
    }

    const year = selectedDate.getFullYear();
    const month = selectedDate.getMonth() + 1;
    const periode = `${year}${month.toString().padStart(2, '0')}`;

    this.subscription.add(
      this.realisationService.getDonneesRealisation(periode).subscribe({
        next: (data) => {
          console.log('=== DONNEES REALISATION RECUES ===', data);
          console.log('=== DONNEES REALISATION ASSIGNEES ===', this.donneesRealisation);
          this.donneesRealisation = data;
          console.log('=== DONNEES REALISATION APRES ASSIGNATION ===', this.donneesRealisation);
          this.setupCharts();
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données:', error);
          this.error = 'Erreur lors du chargement des données';
          this.loading = false;
        }
      })
    );
  }

  /**
   * Configure les graphiques
   */
  private setupCharts() {
    if (!this.donneesRealisation) {
      console.log('=== SETUP CHARTS: Pas de données ===');
      return;
    }

    try {
      console.log('=== SETUP CHARTS: Début ===');
      this.setupProductionGnlChart();
      this.setupEvolutionChart();
      console.log('=== SETUP CHARTS: Terminé ===');
    } catch (error) {
      console.error('=== ERREUR SETUP CHARTS ===', error);
    }
  }

  /**
   * Configure le graphique de production GNL
   */
  private setupProductionGnlChart() {
    if (!this.donneesRealisation?.unites) return;

    const unites = this.donneesRealisation.unites;
    const labels = unites.map(u => u.unite);
    const productionData = unites.map(u => u.productionReelle);
    const objectifData = unites.map(u => u.objectifProduction);

    this.productionGnlChartData = {
      labels: labels,
      datasets: [
        {
          label: 'Production Réelle',
          data: productionData,
          backgroundColor: '#42A5F5',
          borderColor: '#1E88E5',
          borderWidth: 1
        },
        {
          label: 'Objectif',
          data: objectifData,
          backgroundColor: '#66BB6A',
          borderColor: '#43A047',
          borderWidth: 1
        }
      ]
    };

    this.productionGnlChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    };
  }

  /**
   * Configure le graphique d'évolution
   */
  private setupEvolutionChart() {
    if (!this.donneesRealisation?.evolution) return;

    const evolution = this.donneesRealisation.evolution;
    const labels = evolution.map(e => e.mois);
    const productionData = evolution.map(e => e.production);
    const objectifData = evolution.map(e => e.objectif);

    this.evolutionChartData = {
      labels: labels,
      datasets: [
        {
          label: 'Production',
          data: productionData,
          borderColor: '#42A5F5',
          backgroundColor: 'rgba(66, 165, 245, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Objectif',
          data: objectifData,
          borderColor: '#66BB6A',
          backgroundColor: 'rgba(102, 187, 106, 0.1)',
          tension: 0.4,
          fill: true
        }
      ]
    };

    this.evolutionChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true
        }
      }
    };
  }

  /**
   * Calcule le pourcentage de réalisation
   */
  getPourcentageRealisation(production: number, objectif: number): number {
    if (objectif === 0) return 0;
    return Math.round((production / objectif) * 100);
  }

  /**
   * Retourne la classe CSS pour le pourcentage
   */
  getPourcentageClass(pourcentage: number): string {
    if (pourcentage >= 100) return 'text-green-600';
    if (pourcentage >= 80) return 'text-orange-500';
    return 'text-red-600';
  }

  /**
   * Retourne la sévérité pour les tags PrimeNG
   */
  getTagSeverity(pourcentage: number): 'success' | 'warning' | 'danger' {
    if (pourcentage >= 100) return 'success';
    if (pourcentage >= 80) return 'warning';
    return 'danger';
  }
}
