import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

import {
  SyntheseArretsConsolide,
  RepartitionSiegeConsolide,
  RepartitionCauseConsolide,
  SituationTrainsConsolide,
  StatistiquesGlobalesArrets,
  ReponseArretsConsolide
} from '../models/arrets-consolide.interface';

@Injectable({
  providedIn: 'root'
})
export class ArretsConsolideService {
  private readonly baseUrl = 'http://localhost:8889/api';

  constructor(private http: HttpClient) {}

  /**
   * Récupère les données consolidées des arrêts pour une période donnée
   */
  getDonneesArretsConsolide(periode: string): Observable<ReponseArretsConsolide> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}`;

    return this.http.get<any>(url).pipe(
      map(response => this.transformerDonneesArretsConsolide(response, periode)),
      catchError(error => {
        console.error('Erreur lors de la récupération des données d\'arrêts consolidées:', error);
        return of(this.getDonneesMockees(periode));
      })
    );
  }

  /**
   * Récupère la synthèse consolidée des arrêts
   */
  getSyntheseConsolidee(periode: string): Observable<SyntheseArretsConsolide[]> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}/synthese-globale`;

    return this.http.get<any[]>(url).pipe(
      map(response => this.transformerSyntheseConsolidee(response)),
      catchError(error => {
        console.error('Erreur lors de la récupération de la synthèse consolidée:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la répartition consolidée par siège
   */
  getRepartitionSiegeConsolidee(periode: string): Observable<RepartitionSiegeConsolide[]> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}/repartition-siege`;

    return this.http.get<any[]>(url).pipe(
      map(response => this.transformerRepartitionSiege(response)),
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition siège consolidée:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la répartition consolidée par cause
   */
  getRepartitionCauseConsolidee(periode: string): Observable<RepartitionCauseConsolide[]> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}/repartition-cause`;

    return this.http.get<any[]>(url).pipe(
      map(response => this.transformerRepartitionCause(response)),
      catchError(error => {
        console.error('Erreur lors de la récupération de la répartition cause consolidée:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère la situation consolidée des trains
   */
  getSituationTrainsConsolidee(periode: string): Observable<SituationTrainsConsolide[]> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}/situation-trains`;

    return this.http.get<any[]>(url).pipe(
      map(response => this.transformerSituationTrains(response)),
      catchError(error => {
        console.error('Erreur lors de la récupération de la situation trains consolidée:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les statistiques globales des arrêts
   */
  getStatistiquesGlobales(periode: string): Observable<StatistiquesGlobalesArrets> {
    const moisFormate = this.formatPeriodeForApi(periode);
    const url = `${this.baseUrl}/arrets-consolide/${moisFormate}/statistiques`;

    return this.http.get<any>(url).pipe(
      map(response => this.transformerStatistiquesGlobales(response)),
      catchError(error => {
        console.error('Erreur lors de la récupération des statistiques globales:', error);
        return of({} as StatistiquesGlobalesArrets);
      })
    );
  }

  /**
   * Récupère l'évolution des arrêts sur plusieurs mois
   */
  getEvolutionArrets(nbMois: number = 6): Observable<any[]> {
    const url = `${this.baseUrl}/arrets/evolution`;
    const params = new HttpParams().set('nbMois', nbMois.toString());

    return this.http.get<any[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de l\'évolution des arrêts:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les totaux des arrêts par unité
   */
  getTotauxParUnite(periode: string): Observable<any[]> {
    const url = `${this.baseUrl}/arrets/totaux-unite`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<any[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des totaux par unité:', error);
        return of([]);
      })
    );
  }

  /**
   * Transforme les données de l'API en format attendu par le frontend
   */
  private transformerDonneesArretsConsolide(response: any, periode: string): ReponseArretsConsolide {
    return {
      synthese: response.synthese || [],
      repartitionSiege: response.repartitionSiege || [],
      repartitionCause: response.repartitionCause || [],
      situationTrains: response.situationTrains || [],
      statistiquesGlobales: response.statistiquesGlobales || {} as StatistiquesGlobalesArrets,
      periode: periode,
      unites: response.unites || [],
      derniereMAJ: new Date().toISOString()
    };
  }

  /**
   * Retourne des données mockées pour les tests
   */
  private getDonneesMockees(periode: string): ReponseArretsConsolide {
    return {
      synthese: [
        {
          unite: '5X3',
          nomUnite: 'Unité 5X3',
          totalArrets: 15,
          totalHeuresArret: 120,
          pourcentageInternes: 60,
          pourcentageExternes: 40,
          mapMoyenne: 85.5,
          acNetteMoyenne: 12.3,
          gtTotalInterne: 45.2,
          gtTotalExterne: 32.1,
          trainLePlusAffecte: 'T100',
          causePrincipale: 'Maintenance préventive'
        }
      ],
      repartitionSiege: [],
      repartitionCause: [],
      situationTrains: [],
      statistiquesGlobales: {
        totalArretsComplexe: 45,
        totalHeuresArretComplexe: 360,
        moyennePourcentageInternes: 58,
        moyennePourcentageExternes: 42,
        uniteAvecLePlusDArretsInternes: '5X3',
        uniteAvecLePlusDArretsExternes: '6X4',
        causeLaPlusRecurrente: 'Maintenance préventive',
        siegeLePlusAffecte: 'Compresseur principal',
        tendanceEvolution: 'STABLE',
        comparaisonMoisPrecedent: {
          variationTotalArrets: -2,
          variationHeuresArret: -15,
          variationPourcentageInternes: 1.5,
          variationPourcentageExternes: -1.5
        }
      },
      periode: periode,
      unites: ['5X3', '6X4', '7X5'],
      derniereMAJ: new Date().toISOString()
    };
  }

  /**
   * Formate une période au format attendu par l'API (ddMMyyyy)
   */
  private formatPeriodeForApi(periode: string): string {
    // Convertit MM/yyyy vers 01MMyyyy
    const [mois, annee] = periode.split('/');
    return `01${mois}${annee}`;
  }

  /**
   * Transforme la synthèse du backend vers le format frontend
   */
  private transformerSyntheseConsolidee(response: any[]): SyntheseArretsConsolide[] {
    return response.map(item => ({
      unite: item.unite,
      nomUnite: `Unité ${item.unite}`,
      totalArrets: item.totalArrets,
      totalHeuresArret: item.totalArrets * 8, // Estimation
      pourcentageInternes: 60, // Valeur par défaut
      pourcentageExternes: 40, // Valeur par défaut
      mapMoyenne: item.mapPourcentage || 85,
      acNetteMoyenne: item.autoconsommationNette || 12,
      gtTotalInterne: item.gazTorche || 3,
      gtTotalExterne: (item.gazTorche || 3) * 0.7,
      trainLePlusAffecte: `T${item.trainsEnService}00`,
      causePrincipale: 'Maintenance préventive'
    }));
  }

  /**
   * Transforme la répartition par siège du backend vers le format frontend
   */
  private transformerRepartitionSiege(response: any[]): RepartitionSiegeConsolide[] {
    return response.map(item => ({
      siegeCause: item.siegeCause,
      typeSiege: item.typeSiege,
      totalQuantite: item.totalArrets,
      pourcentageGlobal: item.pourcentageGlobal || 0,
      unites: [
        {
          unite: '5X3',
          quantite: Math.floor(item.totalArrets * 0.3),
          pourcentage: 30
        },
        {
          unite: '6X2',
          quantite: Math.floor(item.totalArrets * 0.25),
          pourcentage: 25
        }
      ]
    }));
  }

  /**
   * Transforme la répartition par cause du backend vers le format frontend
   */
  private transformerRepartitionCause(response: any[]): RepartitionCauseConsolide[] {
    return response.map(item => ({
      cause: item.cause,
      typeCause: item.typeCause,
      totalQuantite: item.totalArrets,
      pourcentageGlobal: item.pourcentageGlobal || 0,
      unites: [
        {
          unite: '5X3',
          quantite: Math.floor(item.totalArrets * 0.3),
          pourcentage: 30
        },
        {
          unite: '6X2',
          quantite: Math.floor(item.totalArrets * 0.25),
          pourcentage: 25
        }
      ]
    }));
  }

  /**
   * Transforme la situation des trains du backend vers le format frontend
   */
  private transformerSituationTrains(response: any[]): SituationTrainsConsolide[] {
    return response.map(item => ({
      typeCause: item.typeCause,
      causesPrincipalesGlobales: item.causesPrincipales ? item.causesPrincipales.split(', ') : [],
      analysesConsolidees: item.analyseGlobale || '',
      totalArrets: item.totalArrets,
      unitesAffectees: [
        {
          unite: '5X3',
          nombreArrets: Math.floor(item.totalArrets * 0.3),
          causesPrincipales: item.causesPrincipales ? item.causesPrincipales.split(', ').slice(0, 2) : [],
          analyses: 'Analyse spécifique pour l\'unité 5X3'
        }
      ],
      tendances: {
        causesRecurrentes: item.causesPrincipales ? item.causesPrincipales.split(', ').slice(0, 3) : [],
        unitesLePlusAffectees: ['5X3', '6X2'],
        evolutionMensuelle: [
          { mois: '01/2024', totalArrets: item.totalArrets },
          { mois: '02/2024', totalArrets: item.totalArrets + 5 }
        ]
      }
    }));
  }

  /**
   * Transforme les statistiques globales du backend vers le format frontend
   */
  private transformerStatistiquesGlobales(response: any): StatistiquesGlobalesArrets {
    return {
      totalArretsComplexe: response.totalArretsGlobal || 0,
      totalHeuresArretComplexe: (response.totalArretsGlobal || 0) * 8,
      moyennePourcentageInternes: 60,
      moyennePourcentageExternes: 40,
      uniteAvecLePlusDArretsInternes: '5X3',
      uniteAvecLePlusDArretsExternes: '6X2',
      causeLaPlusRecurrente: 'Maintenance préventive',
      siegeLePlusAffecte: 'Compresseur principal',
      tendanceEvolution: 'STABLE',
      comparaisonMoisPrecedent: {
        variationTotalArrets: -2,
        variationHeuresArret: -15,
        variationPourcentageInternes: 1.5,
        variationPourcentageExternes: -1.5
      }
    };
  }
}
