import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  DonneesAutoconsommation,
  AutoconsommationUnite,
  TotauxAutoconsommation,
  EvolutionAutoconsommation,
  StatistiquesAutoconsommation,
  GazTorcheCause
} from '../../../model/Autoconsommation';

@Injectable({
  providedIn: 'root'
})
export class AutoconsommationService {
  private readonly baseUrl = 'http://localhost:8889/api';

  constructor(private http: HttpClient) {}

  /**
   * Récupère les données d'autoconsommation consolidées pour une période donnée
   */
  getDonneesAutoconsommation(periode: string): Observable<DonneesAutoconsommation> {
    const url = `${this.baseUrl}/autoconsommation/consolide`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<any>(url, { params }).pipe(
      map(response => this.transformerDonneesAutoconsommation(response, periode)),
      catchError(error => {
        console.error('Erreur lors de la récupération des données d\'autoconsommation:', error);
        return of(this.getDonneesMockees(periode));
      })
    );
  }

  /**
   * Récupère les statistiques d'autoconsommation pour une période donnée
   */
  getStatistiquesAutoconsommation(periode: string): Observable<StatistiquesAutoconsommation> {
    const url = `${this.baseUrl}/autoconsommation/statistiques`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<StatistiquesAutoconsommation>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des statistiques d\'autoconsommation:', error);
        return of({
          nombreUnitesAcceptables: 2,
          nombreUnitesASurveiller: 1,
          nombreUnitesCritiques: 0,
          performanceGlobale: 'ACCEPTABLE' as const
        });
      })
    );
  }

  /**
   * Récupère les données d'autoconsommation par unité
   */
  getAutoconsommationParUnite(periode: string): Observable<AutoconsommationUnite[]> {
    const url = `${this.baseUrl}/autoconsommation/unites`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<AutoconsommationUnite[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de l\'autoconsommation par unité:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère les données de gaz torché par cause
   */
  getGazTorcheParCause(periode: string): Observable<GazTorcheCause[]> {
    const url = `${this.baseUrl}/autoconsommation/gaz-torche-causes`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<GazTorcheCause[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération du gaz torché par cause:', error);
        return of(this.getGazTorcheCausesMockees());
      })
    );
  }

  /**
   * Récupère l'évolution mensuelle de l'autoconsommation
   */
  getEvolutionAutoconsommation(periode: string, nombreMois: number = 6): Observable<EvolutionAutoconsommation[]> {
    const url = `${this.baseUrl}/autoconsommation/evolution`;
    const params = new HttpParams()
      .set('periode', periode)
      .set('nombreMois', nombreMois.toString());

    return this.http.get<EvolutionAutoconsommation[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de l\'évolution d\'autoconsommation:', error);
        return of(this.getEvolutionMockee());
      })
    );
  }

  /**
   * Transforme les données de l'API en format attendu par le frontend
   */
  private transformerDonneesAutoconsommation(response: any, periode: string): DonneesAutoconsommation {
    console.log('=== TRANSFORMATION DONNEES AUTOCONSOMMATION ===', response);

    // L'API retourne déjà la bonne structure, on fait juste une conversion de types
    const unites: AutoconsommationUnite[] = (response.unites || []).map((item: any) => ({
      uniteCode: item.uniteCode || '',
      unite: item.unite || '',
      statut: item.statut || 'ACTIF',
      gnRecu: item.gnRecu || 0,
      autoconsommationGlobale: item.autoconsommationGlobale || 0,
      autoconsommationNette: item.autoconsommationNette || 0,
      gazTorcheTotal: item.gazTorcheTotal || 0,
      gazTorcheInterne: item.gazTorcheInterne || 0,
      gazTorcheExterne: item.gazTorcheExterne || 0,
      tauxAutoconsommationGlobale: item.tauxAutoconsommationGlobale || 0,
      tauxAutoconsommationNette: item.tauxAutoconsommationNette || 0,
      tauxGazTorche: item.tauxGazTorche || 0,
      tauxObjectif: item.tauxObjectif || 8.0,
      ecartObjectif: item.ecartObjectif || 0
    }));

    const totaux: TotauxAutoconsommation = {
      totalGnRecu: response.totaux?.totalGnRecu || 0,
      totalAutoconsommationGlobale: response.totaux?.totalAutoconsommationGlobale || 0,
      totalAutoconsommationNette: response.totaux?.totalAutoconsommationNette || 0,
      totalGazTorche: response.totaux?.totalGazTorche || 0,
      totalGazTorcheInterne: response.totaux?.totalGazTorcheInterne || 0,
      totalGazTorcheExterne: response.totaux?.totalGazTorcheExterne || 0,
      tauxGlobalAutoconsommationGlobale: response.totaux?.tauxGlobalAutoconsommationGlobale || 0,
      tauxGlobalAutoconsommationNette: response.totaux?.tauxGlobalAutoconsommationNette || 0,
      tauxGlobalGazTorche: response.totaux?.tauxGlobalGazTorche || 0,
      pourcentageGazTorcheInterne: response.totaux?.pourcentageGazTorcheInterne || 0,
      pourcentageGazTorcheExterne: response.totaux?.pourcentageGazTorcheExterne || 0
    };

    const evolution: EvolutionAutoconsommation[] = (response.evolution || []).map((item: any) => ({
      mois: item.mois || '',
      autoconsommationNette: item.autoconsommationNette || 0,
      gazTorche: item.gazTorche || 0,
      tauxAutoconsommationNette: item.tauxAutoconsommationNette || 0,
      tauxGazTorche: item.tauxGazTorche || 0
    }));

    // Utiliser les statistiques de l'API au lieu de les recalculer
    const statistiques: StatistiquesAutoconsommation = {
      nombreUnitesAcceptables: response.statistiques?.nombreUnitesAcceptables || 0,
      nombreUnitesASurveiller: response.statistiques?.nombreUnitesASurveiller || 0,
      nombreUnitesCritiques: response.statistiques?.nombreUnitesCritiques || 0,
      performanceGlobale: response.statistiques?.performanceGlobale || 'ACCEPTABLE'
    };

    return {
      periode: response.periode || periode,
      derniereMiseAJour: new Date(response.derniereMiseAJour || Date.now()),
      statistiques,
      unites,
      gazTorcheCauses: response.gazTorcheCauses || [], // Utiliser les vraies données de l'API
      totaux,
      evolution
    };
  }

  /**
   * Calcule les statistiques d'autoconsommation
   */
  private calculerStatistiques(unites: AutoconsommationUnite[]): StatistiquesAutoconsommation {
    const unitesAcceptables = unites.filter(u => u.tauxAutoconsommationNette <= 8).length;
    const unitesASurveiller = unites.filter(u => u.tauxAutoconsommationNette > 8 && u.tauxAutoconsommationNette <= 12).length;
    const unitesCritiques = unites.filter(u => u.tauxAutoconsommationNette > 12).length;

    let performanceGlobale: 'ACCEPTABLE' | 'A_SURVEILLER' | 'CRITIQUE';
    if (unitesCritiques > 0) {
      performanceGlobale = 'CRITIQUE';
    } else if (unitesASurveiller > 0) {
      performanceGlobale = 'A_SURVEILLER';
    } else {
      performanceGlobale = 'ACCEPTABLE';
    }

    return {
      nombreUnitesAcceptables: unitesAcceptables,
      nombreUnitesASurveiller: unitesASurveiller,
      nombreUnitesCritiques: unitesCritiques,
      performanceGlobale
    };
  }

  /**
   * Retourne des données mockées pour les tests
   */
  private getDonneesMockees(periode: string): DonneesAutoconsommation {
    const unites: AutoconsommationUnite[] = [
      {
        uniteCode: 'U1',
        unite: 'Unité 1',
        statut: 'ACTIF',
        gnRecu: 50000,
        autoconsommationGlobale: 12000,
        autoconsommationNette: 10000,
        gazTorcheTotal: 2000,
        gazTorcheInterne: 1200,
        gazTorcheExterne: 800,
        tauxAutoconsommationGlobale: 12.0,
        tauxAutoconsommationNette: 10.0,
        tauxGazTorche: 2.0,
        tauxObjectif: 8.0,
        ecartObjectif: 2.0
      }
    ];

    return {
      periode,
      derniereMiseAJour: new Date(),
      statistiques: this.calculerStatistiques(unites),
      unites,
      gazTorcheCauses: this.getGazTorcheCausesMockees(),
      totaux: {
        totalGnRecu: 150000,
        totalAutoconsommationGlobale: 12000,
        totalAutoconsommationNette: 10000,
        totalGazTorche: 2000,
        totalGazTorcheInterne: 1200,
        totalGazTorcheExterne: 800,
        tauxGlobalAutoconsommationGlobale: 12.0,
        tauxGlobalAutoconsommationNette: 10.0,
        tauxGlobalGazTorche: 2.0,
        pourcentageGazTorcheInterne: 60.0,
        pourcentageGazTorcheExterne: 40.0
      },
      evolution: this.getEvolutionMockee()
    };
  }

  /**
   * Retourne des données de gaz torché par cause mockées
   */
  private getGazTorcheCausesMockees(): GazTorcheCause[] {
    return [
      { cause: 'Maintenance programmée', type: 'INTERNE', quantite: 800, pourcentage: 40.0 },
      { cause: 'Arrêt d\'urgence', type: 'INTERNE', quantite: 400, pourcentage: 20.0 },
      { cause: 'Problème réseau', type: 'EXTERNE', quantite: 500, pourcentage: 25.0 },
      { cause: 'Contrainte client', type: 'EXTERNE', quantite: 300, pourcentage: 15.0 }
    ];
  }

  /**
   * Retourne des données d'évolution mockées
   */
  private getEvolutionMockee(): EvolutionAutoconsommation[] {
    return [
      { mois: 'Juil 2024', autoconsommationNette: 9500, gazTorche: 1800, tauxAutoconsommationNette: 9.5, tauxGazTorche: 1.8 },
      { mois: 'Août 2024', autoconsommationNette: 9800, gazTorche: 1900, tauxAutoconsommationNette: 9.8, tauxGazTorche: 1.9 },
      { mois: 'Sept 2024', autoconsommationNette: 9200, gazTorche: 1700, tauxAutoconsommationNette: 9.2, tauxGazTorche: 1.7 },
      { mois: 'Oct 2024', autoconsommationNette: 9600, gazTorche: 1850, tauxAutoconsommationNette: 9.6, tauxGazTorche: 1.85 },
      { mois: 'Nov 2024', autoconsommationNette: 9500, gazTorche: 1800, tauxAutoconsommationNette: 9.5, tauxGazTorche: 1.8 },
      { mois: 'Déc 2024', autoconsommationNette: 10000, gazTorche: 2000, tauxAutoconsommationNette: 10.0, tauxGazTorche: 2.0 }
    ];
  }
}
