import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { DashboardConsolideService } from './dashboard-consolide.service';
import { environment } from '../../../environments/environment';

describe('DashboardConsolideService', () => {
  let service: DashboardConsolideService;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [DashboardConsolideService]
    });
    service = TestBed.inject(DashboardConsolideService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get dashboard consolidé data', () => {
    const pmois = '01122024';
    const mockUnites = [
      { id: 1, codeUnite: '5X2', unite: 'GL1Z', pmois: '2024-12-01', tauxAcObj: 19, tauxAcDesign: 14.8 },
      { id: 2, codeUnite: '5X3', unite: 'GL2Z', pmois: '2024-12-01', tauxAcObj: 18, tauxAcDesign: 15.2 }
    ];

    const mockAutoCons = [
      {
        unite: '5X2',
        autoConsMoisNet: 15000,
        receptionGnMois: 120000,
        transformeGnMois: 100000,
        gazTorcheeMois: 5000,
        tauxAutoConsMoisGlobal: 12.5
      }
    ];

    const mockGazTorches = [
      {
        unite: '5X2',
        codeCauseTorchage: 'I',
        quantiteGazTorchee: 3000
      },
      {
        unite: '5X2',
        codeCauseTorchage: 'E',
        quantiteGazTorchee: 2000
      }
    ];

    service.getDashboardConsolide(pmois).subscribe(dashboard => {
      expect(dashboard).toBeTruthy();
      expect(dashboard.periode).toBe(pmois);
      expect(dashboard.repartitionGnRecu).toBeDefined();
      expect(dashboard.productionGnl).toBeDefined();
      expect(dashboard.autoconsommation).toBeDefined();
      expect(dashboard.totauxRealisation).toBeDefined();
      expect(dashboard.totauxAutoconsommation).toBeDefined();
    });

    // Mock des requêtes HTTP
    const unitesReq = httpMock.expectOne(`${environment.apiUrl}/api/list-unites/${pmois}`);
    expect(unitesReq.request.method).toBe('GET');
    unitesReq.flush(mockUnites);

    // Mock des requêtes d'autoconsommation pour chaque unité
    const autoConsReq1 = httpMock.expectOne(`${environment.apiUrl}/api/auto-cons-mens/${pmois}/5X1`);
    autoConsReq1.flush(null);

    const autoConsReq2 = httpMock.expectOne(`${environment.apiUrl}/api/auto-cons-mens/${pmois}/5X2`);
    autoConsReq2.flush(mockAutoCons[0]);

    const autoConsReq3 = httpMock.expectOne(`${environment.apiUrl}/api/auto-cons-mens/${pmois}/5X3`);
    autoConsReq3.flush(null);

    const autoConsReq4 = httpMock.expectOne(`${environment.apiUrl}/api/auto-cons-mens/${pmois}/5X8`);
    autoConsReq4.flush(null);

    // Mock des requêtes de gaz torchés pour chaque unité
    const gazTorchesReq1 = httpMock.expectOne(`${environment.apiUrl}/api/gaz-torchee-par-cause-torchage/5X1/${pmois}`);
    gazTorchesReq1.flush([]);

    const gazTorchesReq2 = httpMock.expectOne(`${environment.apiUrl}/api/gaz-torchee-par-cause-torchage/5X2/${pmois}`);
    gazTorchesReq2.flush(mockGazTorches);

    const gazTorchesReq3 = httpMock.expectOne(`${environment.apiUrl}/api/gaz-torchee-par-cause-torchage/5X3/${pmois}`);
    gazTorchesReq3.flush([]);

    const gazTorchesReq4 = httpMock.expectOne(`${environment.apiUrl}/api/gaz-torchee-par-cause-torchage/5X8/${pmois}`);
    gazTorchesReq4.flush([]);
  });

  it('should handle errors gracefully', () => {
    const pmois = '01122024';

    service.getDashboardConsolide(pmois).subscribe(dashboard => {
      expect(dashboard).toBeTruthy();
      expect(dashboard.repartitionGnRecu).toEqual([]);
      expect(dashboard.productionGnl).toEqual([]);
      expect(dashboard.autoconsommation).toEqual([]);
    });

    // Simuler une erreur sur la requête des unités
    const unitesReq = httpMock.expectOne(`${environment.apiUrl}/api/list-unites/${pmois}`);
    unitesReq.error(new ErrorEvent('Network error'));

    // Les autres requêtes ne devraient pas être appelées en cas d'erreur
  });

  it('should calculate totals correctly', () => {
    const pmois = '01122024';
    
    // Test avec des données mockées
    service.getDashboardConsolide(pmois).subscribe(dashboard => {
      expect(dashboard.totauxRealisation.totalGnRecu).toBeGreaterThanOrEqual(0);
      expect(dashboard.totauxAutoconsommation.totalAutoconsommationNette).toBeGreaterThanOrEqual(0);
      expect(dashboard.nombreUnitesActives).toBeGreaterThanOrEqual(0);
      expect(dashboard.nombreUnitesArretTotal).toBeGreaterThanOrEqual(0);
      expect(dashboard.nombreUnitesMaintenance).toBeGreaterThanOrEqual(0);
    });

    // Mock minimal pour tester les calculs
    const unitesReq = httpMock.expectOne(`${environment.apiUrl}/api/list-unites/${pmois}`);
    unitesReq.flush([]);

    // Mock des requêtes vides
    ['5X1', '5X2', '5X3', '5X8'].forEach(unite => {
      const autoConsReq = httpMock.expectOne(`${environment.apiUrl}/api/auto-cons-mens/${pmois}/${unite}`);
      autoConsReq.flush(null);

      const gazTorchesReq = httpMock.expectOne(`${environment.apiUrl}/api/gaz-torchee-par-cause-torchage/${unite}/${pmois}`);
      gazTorchesReq.flush([]);
    });
  });
});
