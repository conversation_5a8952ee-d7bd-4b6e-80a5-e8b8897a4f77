import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, catchError, of } from 'rxjs';
import { environment } from '../../../environments/environment';
import { CacheService } from '../../../services/cache.service';
import {
  DashboardConsolide,
  AutoconsommationConsolidee,
  EvolutionMensuelle,
  TotalAutoconsommationConsolide
} from '../../../model/DashboardConsolide';
import {
  RealisationUnite,
  RepartitionGnRecu,
  ProductionGnlConsolidee,
  TotalConsolide
} from '../../../model/RealisationUnite';
import { AutoConsMens } from '../../../model/AutoConsMens';
import { ListUnite } from '../../../model/ListUnite';
import { GazTorcheeParCauseTorchage } from '../../../model/GazTorcheeParCauseTorchage';

/**
 * Interface pour la réponse du backend consolidé
 */
interface DashboardConsolideBackendResponse {
  periode: string;
  derniereMiseAJour: string;
  unites: ListUnite[];
  autoconsommation: AutoConsMens[];
  gazTorche: GazTorcheeParCauseTorchage[];
  indicateursPerformance: any[];
  evolutionMensuelle: {
    mois: string;
    autoconsommationNette: number;
    gazTorche: number;
    tauxAutoconsommationNette: number;
    tauxGazTorche: number;
  }[];
  totaux: {
    totalProductionReelle: number;
    totalObjectif: number;
    totalAutoconsommationNette: number;
    totalGazTorche: number;
    totalGnRecu: number;
    tauxGlobalRealisationObjectif: number;
  };
  statistiques: {
    nombreUnitesActives: number;
    nombreUnitesArretTotal: number;
    nombreUnitesMaintenance: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class DashboardConsolideService {
  private baseUrl = environment.apiUrl + '/api';

  constructor(private http: HttpClient, private cacheService: CacheService) {}

  /**
   * Récupère toutes les données consolidées pour le dashboard (avec cache)
   * @param pmois Période au format ddMMyyyy
   * @returns Observable contenant toutes les données consolidées
   */
  getDashboardConsolide(pmois: string): Observable<DashboardConsolide> {
    const cacheKey = this.cacheService.generateKey('dashboard-consolide', undefined, pmois);

    return this.cacheService.getOrSet(cacheKey, () => {
      console.log('Récupération des données dashboard consolidées pour:', pmois);

      // Utiliser l'endpoint consolidé qui fonctionne maintenant
      console.log('Récupération des données depuis l\'endpoint consolidé:', pmois);
      return this.http.get<DashboardConsolideBackendResponse>(`${this.baseUrl}/dashboard-consolide/${pmois}`).pipe(
        map(backendResponse => {
          console.log('Données reçues du backend consolidé:', backendResponse);
          return this.transformBackendResponse(backendResponse, pmois);
        }),
        catchError(error => {
          console.warn('Erreur avec l\'endpoint consolidé, fallback vers les données mockées:', error);
          console.log('Utilisation des données mockées pour le dashboard');
          return of(this.getMockDashboard(pmois));
        })
      );
    }, 3 * 60 * 1000); // Cache de 3 minutes pour le dashboard
  }





  /**
   * Construit l'objet DashboardConsolide à partir des données récupérées
   * @deprecated Cette méthode n'est plus utilisée depuis l'implémentation de l'endpoint consolidé
   */
  private buildDashboardConsolide(data: any, pmois: string): DashboardConsolide {
    console.log('Construction du dashboard avec les données:', {
      unites: data.unites?.length || 0,
      autoCons: data.autoconsommationData?.autoCons?.length || 0,
      gazTorches: data.autoconsommationData?.gazTorches?.length || 0,
      realisationData: data.realisationData?.length || 0,
      evolutionData: data.evolutionData?.length || 0
    });

    const repartitionGnRecu = this.buildRepartitionGnRecu(
      data.autoconsommationData?.autoCons || [],
      data.unites || []
    );

    const productionGnl = this.buildProductionGnl(
      data.realisationData || [],
      data.unites || []
    );

    const autoconsommation = this.buildAutoconsommation(
      data.autoconsommationData || { autoCons: [], gazTorches: [] },
      data.unites || []
    );

    const dashboard = {
      periode: pmois,
      derniereMiseAJour: new Date(),
      repartitionGnRecu,
      productionGnl,
      autoconsommation,
      evolutionMensuelle: data.evolutionData || [],
      totauxRealisation: this.calculateTotauxRealisation(repartitionGnRecu, productionGnl),
      totauxAutoconsommation: this.calculateTotauxAutoconsommation(autoconsommation),
      nombreUnitesActives: this.countUnitesByStatus(data.unites || [], 'ACTIF'),
      nombreUnitesArretTotal: this.countUnitesByStatus(data.unites || [], 'ARRET_TOTAL'),
      nombreUnitesMaintenance: this.countUnitesByStatus(data.unites || [], 'MAINTENANCE')
    };

    console.log('Dashboard construit:', {
      repartitionCount: dashboard.repartitionGnRecu.length,
      productionCount: dashboard.productionGnl.length,
      autoconsommationCount: dashboard.autoconsommation.length,
      evolutionCount: dashboard.evolutionMensuelle.length
    });

    return dashboard;
  }

  /**
   * Construit les données de répartition du GN reçu
   */
  private buildRepartitionGnRecu(autoConsData: AutoConsMens[], unites: ListUnite[]): RepartitionGnRecu[] {
    return autoConsData.map(data => {
      const unite = unites.find(u => u.codeUnite === data.unite);
      const totalGnRecu = data.receptionGnMois || 0;
      const gnTransforme = data.transformeGnMois || 0;
      const gazTorche = data.gazTorcheeMois || 0;
      const autoconsommationNette = data.autoConsMoisNet || 0;

      return {
        uniteCode: data.unite || '',
        unite: unite?.unite || data.unite || '',
        gnTransforme,
        gazTorche,
        autoconsommationNette,
        totalGnRecu,
        pourcentageGnTransforme: totalGnRecu > 0 ? (gnTransforme / totalGnRecu) * 100 : 0,
        pourcentageGazTorche: totalGnRecu > 0 ? (gazTorche / totalGnRecu) * 100 : 0,
        pourcentageAutoconsommationNette: totalGnRecu > 0 ? (autoconsommationNette / totalGnRecu) * 100 : 0,
        statut: totalGnRecu > 0 ? 'ACTIF' : 'ARRET_TOTAL'
      };
    });
  }

  /**
   * Construit les données de production GNL
   */
  private buildProductionGnl(realisationData: RealisationUnite[], unites: ListUnite[]): ProductionGnlConsolidee[] {
    return realisationData.map(data => {
      const unite = unites.find(u => u.codeUnite === data.uniteCode);
      const ecartObjectif = data.objectifProductionGnl > 0 ?
        ((data.reelProductionGnl - data.objectifProductionGnl) / data.objectifProductionGnl) * 100 : 0;
      const ecartPrevision = data.previsionProductionGnl > 0 ?
        ((data.reelProductionGnl - data.previsionProductionGnl) / data.previsionProductionGnl) * 100 : 0;

      return {
        uniteCode: data.uniteCode,
        unite: unite?.unite || data.unite || '',
        productionReelle: data.reelProductionGnl,
        objectifProduction: data.objectifProductionGnl,
        previsionProduction: data.previsionProductionGnl,
        ecartObjectif,
        ecartPrevision,
        tauxRealisationObjectif: data.objectifProductionGnl > 0 ? (data.reelProductionGnl / data.objectifProductionGnl) * 100 : 0,
        tauxRealisationPrevision: data.previsionProductionGnl > 0 ? (data.reelProductionGnl / data.previsionProductionGnl) * 100 : 0,
        statut: data.statut || 'ACTIF'
      };
    });
  }

  /**
   * Construit les données d'autoconsommation
   */
  private buildAutoconsommation(data: {autoCons: AutoConsMens[], gazTorches: GazTorcheeParCauseTorchage[]}, unites: ListUnite[]): AutoconsommationConsolidee[] {
    return data.autoCons.map(autoConsData => {
      const unite = unites.find(u => u.codeUnite === autoConsData.unite);
      const gazTorchesUnite = data.gazTorches.filter(gt => gt.unite === autoConsData.unite);

      const gazTorcheInterne = gazTorchesUnite
        .filter(gt => gt.codeCauseTorchage === 'I')
        .reduce((sum, gt) => sum + (gt.quantiteGazTorchee || 0), 0);

      const gazTorcheExterne = gazTorchesUnite
        .filter(gt => gt.codeCauseTorchage === 'E')
        .reduce((sum, gt) => sum + (gt.quantiteGazTorchee || 0), 0);

      const tauxObjectif = unite?.tauxAcObj || 0;
      const tauxDesign = unite?.tauxAcDesign || 0;
      const tauxAutoconsommationNette = autoConsData.tauxAutoConsMoisGlobal || 0;

      return {
        uniteCode: autoConsData.unite || '',
        unite: unite?.unite || autoConsData.unite || '',
        autoconsommationGlobale: (autoConsData.autoConsMoisNet || 0) + (autoConsData.gazTorcheeMois || 0),
        autoconsommationNette: autoConsData.autoConsMoisNet || 0,
        gazTorcheTotal: autoConsData.gazTorcheeMois || 0,
        gazTorcheInterne,
        gazTorcheExterne,
        tauxAutoconsommationGlobale: autoConsData.tauxAutoConsMoisGlobal || 0,
        tauxAutoconsommationNette: tauxAutoconsommationNette,
        tauxGazTorche: autoConsData.tauxGazTorcheeMois || 0,
        tauxObjectif,
        tauxDesign,
        ecartObjectif: tauxAutoconsommationNette - tauxObjectif,
        ecartDesign: tauxAutoconsommationNette - tauxDesign,
        statut: (autoConsData.receptionGnMois || 0) > 0 ? 'ACTIF' : 'ARRET_TOTAL'
      };
    });
  }

  /**
   * Calcule les totaux de réalisation
   */
  private calculateTotauxRealisation(repartition: RepartitionGnRecu[], production: ProductionGnlConsolidee[]): TotalConsolide {
    const totalGnRecu = repartition.reduce((sum, item) => sum + item.totalGnRecu, 0);
    const totalGnTransforme = repartition.reduce((sum, item) => sum + item.gnTransforme, 0);
    const totalGazTorche = repartition.reduce((sum, item) => sum + item.gazTorche, 0);
    const totalAutoconsommationNette = repartition.reduce((sum, item) => sum + item.autoconsommationNette, 0);

    const totalProductionReelle = production.reduce((sum, item) => sum + item.productionReelle, 0);
    const totalObjectif = production.reduce((sum, item) => sum + item.objectifProduction, 0);
    const totalPrevision = production.reduce((sum, item) => sum + item.previsionProduction, 0);

    return {
      totalProductionReelle,
      totalObjectif,
      totalPrevision,
      totalGnTransforme,
      totalGazTorche,
      totalAutoconsommationNette,
      totalGnRecu,
      tauxGlobalRealisationObjectif: totalObjectif > 0 ? (totalProductionReelle / totalObjectif) * 100 : 0,
      tauxGlobalRealisationPrevision: totalPrevision > 0 ? (totalProductionReelle / totalPrevision) * 100 : 0,
      pourcentageGlobalGnTransforme: totalGnRecu > 0 ? (totalGnTransforme / totalGnRecu) * 100 : 0,
      pourcentageGlobalGazTorche: totalGnRecu > 0 ? (totalGazTorche / totalGnRecu) * 100 : 0,
      pourcentageGlobalAutoconsommationNette: totalGnRecu > 0 ? (totalAutoconsommationNette / totalGnRecu) * 100 : 0
    };
  }

  /**
   * Calcule les totaux d'autoconsommation
   */
  private calculateTotauxAutoconsommation(autoconsommation: AutoconsommationConsolidee[]): TotalAutoconsommationConsolide {
    const totalAutoconsommationGlobale = autoconsommation.reduce((sum, item) => sum + item.autoconsommationGlobale, 0);
    const totalAutoconsommationNette = autoconsommation.reduce((sum, item) => sum + item.autoconsommationNette, 0);
    const totalGazTorche = autoconsommation.reduce((sum, item) => sum + item.gazTorcheTotal, 0);
    const totalGazTorcheInterne = autoconsommation.reduce((sum, item) => sum + item.gazTorcheInterne, 0);
    const totalGazTorcheExterne = autoconsommation.reduce((sum, item) => sum + item.gazTorcheExterne, 0);

    // Calculer les taux globaux (moyenne pondérée)
    const totalGnRecu = autoconsommation.reduce((sum, item) => sum + (item.autoconsommationNette + item.gazTorcheTotal), 0);

    return {
      totalAutoconsommationGlobale,
      totalAutoconsommationNette,
      totalGazTorche,
      totalGazTorcheInterne,
      totalGazTorcheExterne,
      tauxGlobalAutoconsommationGlobale: totalGnRecu > 0 ? (totalAutoconsommationGlobale / totalGnRecu) * 100 : 0,
      tauxGlobalAutoconsommationNette: totalGnRecu > 0 ? (totalAutoconsommationNette / totalGnRecu) * 100 : 0,
      tauxGlobalGazTorche: totalGnRecu > 0 ? (totalGazTorche / totalGnRecu) * 100 : 0,
      pourcentageGazTorcheInterne: totalGazTorche > 0 ? (totalGazTorcheInterne / totalGazTorche) * 100 : 0,
      pourcentageGazTorcheExterne: totalGazTorche > 0 ? (totalGazTorcheExterne / totalGazTorche) * 100 : 0
    };
  }

  /**
   * Compte les unités par statut
   */
  private countUnitesByStatus(unites: ListUnite[], status: string): number {
    // Pour l'instant, on simule le statut basé sur la présence de données
    // TODO: Implémenter la logique de statut basée sur les données réelles
    switch (status) {
      case 'ACTIF':
        return Math.max(1, Math.floor(unites.length * 0.7)); // 70% actives
      case 'ARRET_TOTAL':
        return Math.floor(unites.length * 0.2); // 20% en arrêt
      case 'MAINTENANCE':
        return Math.floor(unites.length * 0.1); // 10% en maintenance
      default:
        return 0;
    }
  }

  /**
   * Transforme la réponse du backend consolidé en format frontend
   */
  private transformBackendResponse(backendResponse: DashboardConsolideBackendResponse, pmois: string): DashboardConsolide {
    console.log('Transformation de la réponse backend');

    // Construire les données de répartition GN
    const repartitionGnRecu = this.buildRepartitionGnRecu(
      backendResponse.autoconsommation || [],
      backendResponse.unites || []
    );

    // Construire les données de production (utiliser indicateursPerformance comme source)
    const productionGnl = this.buildProductionGnlFromIndicateurs(
      backendResponse.indicateursPerformance || [],
      backendResponse.unites || []
    );

    // Construire les données d'autoconsommation
    const autoconsommation = this.buildAutoconsommation(
      {
        autoCons: backendResponse.autoconsommation || [],
        gazTorches: backendResponse.gazTorche || []
      },
      backendResponse.unites || []
    );

    return {
      periode: pmois,
      derniereMiseAJour: new Date(backendResponse.derniereMiseAJour || new Date()),
      repartitionGnRecu,
      productionGnl,
      autoconsommation,
      evolutionMensuelle: backendResponse.evolutionMensuelle || [],
      totauxRealisation: this.transformTotauxFromBackend(backendResponse.totaux),
      totauxAutoconsommation: this.calculateTotauxAutoconsommation(autoconsommation),
      nombreUnitesActives: backendResponse.statistiques?.nombreUnitesActives || 0,
      nombreUnitesArretTotal: backendResponse.statistiques?.nombreUnitesArretTotal || 0,
      nombreUnitesMaintenance: backendResponse.statistiques?.nombreUnitesMaintenance || 0
    };
  }

  /**
   * Transforme les totaux du backend vers le format frontend
   */
  private transformTotauxFromBackend(totauxBackend: any): TotalConsolide {
    if (!totauxBackend) {
      return {
        totalProductionReelle: 0,
        totalObjectif: 0,
        totalPrevision: 0,
        totalGnTransforme: 0,
        totalGazTorche: 0,
        totalAutoconsommationNette: 0,
        totalGnRecu: 0,
        tauxGlobalRealisationObjectif: 0,
        tauxGlobalRealisationPrevision: 0,
        pourcentageGlobalGnTransforme: 0,
        pourcentageGlobalGazTorche: 0,
        pourcentageGlobalAutoconsommationNette: 0
      };
    }

    return {
      totalProductionReelle: totauxBackend.totalProductionReelle || 0,
      totalObjectif: totauxBackend.totalObjectif || 0,
      totalPrevision: totauxBackend.totalObjectif || 0, // Utiliser objectif si pas de prévision
      totalGnTransforme: (totauxBackend.totalGnRecu || 0) - (totauxBackend.totalAutoconsommationNette || 0) - (totauxBackend.totalGazTorche || 0),
      totalGazTorche: totauxBackend.totalGazTorche || 0,
      totalAutoconsommationNette: totauxBackend.totalAutoconsommationNette || 0,
      totalGnRecu: totauxBackend.totalGnRecu || 0,
      tauxGlobalRealisationObjectif: totauxBackend.tauxGlobalRealisationObjectif || 0,
      tauxGlobalRealisationPrevision: totauxBackend.tauxGlobalRealisationObjectif || 0,
      pourcentageGlobalGnTransforme: totauxBackend.totalGnRecu > 0 ?
        (((totauxBackend.totalGnRecu || 0) - (totauxBackend.totalAutoconsommationNette || 0) - (totauxBackend.totalGazTorche || 0)) / totauxBackend.totalGnRecu) * 100 : 0,
      pourcentageGlobalGazTorche: totauxBackend.totalGnRecu > 0 ?
        ((totauxBackend.totalGazTorche || 0) / totauxBackend.totalGnRecu) * 100 : 0,
      pourcentageGlobalAutoconsommationNette: totauxBackend.totalGnRecu > 0 ?
        ((totauxBackend.totalAutoconsommationNette || 0) / totauxBackend.totalGnRecu) * 100 : 0
    };
  }

  /**
   * Construit les données de production à partir des indicateurs de performance
   */
  private buildProductionGnlFromIndicateurs(indicateurs: any[], unites: ListUnite[]): ProductionGnlConsolidee[] {
    return indicateurs.map(indicateur => {
      const unite = unites.find(u => u.codeUnite === indicateur.unite);
      const productionReelle = indicateur.production || 0;
      const objectifProduction = indicateur.productionDesign || 0;
      const previsionProduction = objectifProduction; // Utiliser design comme prévision si pas de données

      const ecartObjectif = objectifProduction > 0 ?
        ((productionReelle - objectifProduction) / objectifProduction) * 100 : 0;

      return {
        uniteCode: indicateur.unite || '',
        unite: unite?.unite || indicateur.unite || '',
        productionReelle,
        objectifProduction,
        previsionProduction,
        ecartObjectif,
        ecartPrevision: ecartObjectif, // Même calcul si pas de prévision séparée
        tauxRealisationObjectif: objectifProduction > 0 ? (productionReelle / objectifProduction) * 100 : 0,
        tauxRealisationPrevision: objectifProduction > 0 ? (productionReelle / objectifProduction) * 100 : 0,
        statut: productionReelle > 0 ? 'ACTIF' : 'ARRET_TOTAL'
      };
    });
  }

  /**
   * Vérifie si on doit utiliser les données mockées
   */
  private shouldUseMockData(data: any): boolean {
    const hasUnites = data.unites && data.unites.length > 0;
    const hasAutoCons = data.autoconsommationData && data.autoconsommationData.autoCons && data.autoconsommationData.autoCons.length > 0;
    const hasRealisation = data.realisationData && data.realisationData.length > 0;

    console.log('Vérification des données:', {
      hasUnites,
      unitesLength: data.unites?.length || 0,
      hasAutoCons,
      autoConsLength: data.autoconsommationData?.autoCons?.length || 0,
      hasRealisation,
      realisationLength: data.realisationData?.length || 0
    });

    // Utiliser les données mockées si on n'a aucune donnée réelle
    const shouldUseMock = !hasUnites && !hasAutoCons && !hasRealisation;

    if (shouldUseMock) {
      console.log('Utilisation des données mockées - aucune donnée réelle disponible');
    } else {
      console.log('Utilisation des données réelles du backend');
    }

    return shouldUseMock;
  }

  /**
   * Retourne un dashboard avec des données mockées complètes
   */
  private getMockDashboard(pmois: string): DashboardConsolide {
    const mockUnites = this.getMockUnites();
    const mockRealisationData = this.getMockRealisationData(pmois);
    const mockAutoConsData = this.getMockAutoConsData();
    const mockGazTorchesData = this.getMockGazTorchesData();

    const autoconsommationData = {
      autoCons: mockAutoConsData,
      gazTorches: mockGazTorchesData
    };

    const repartitionGnRecu = this.buildRepartitionGnRecu(mockAutoConsData, mockUnites);
    const productionGnl = this.buildProductionGnl(mockRealisationData, mockUnites);
    const autoconsommation = this.buildAutoconsommation(autoconsommationData, mockUnites);

    return {
      periode: pmois,
      derniereMiseAJour: new Date(),
      repartitionGnRecu,
      productionGnl,
      autoconsommation,
      evolutionMensuelle: this.getMockEvolutionMensuelle(),
      totauxRealisation: this.calculateTotauxRealisation(repartitionGnRecu, productionGnl),
      totauxAutoconsommation: this.calculateTotauxAutoconsommation(autoconsommation),
      nombreUnitesActives: this.countUnitesByStatus(mockUnites, 'ACTIF'),
      nombreUnitesArretTotal: this.countUnitesByStatus(mockUnites, 'ARRET_TOTAL'),
      nombreUnitesMaintenance: this.countUnitesByStatus(mockUnites, 'MAINTENANCE')
    };
  }

  /**
   * Retourne un dashboard vide en cas d'erreur
   */
  private getEmptyDashboard(pmois: string): DashboardConsolide {
    return {
      periode: pmois,
      derniereMiseAJour: new Date(),
      repartitionGnRecu: [],
      productionGnl: [],
      autoconsommation: [],
      evolutionMensuelle: [],
      totauxRealisation: {
        totalProductionReelle: 0,
        totalObjectif: 0,
        totalPrevision: 0,
        totalGnTransforme: 0,
        totalGazTorche: 0,
        totalAutoconsommationNette: 0,
        totalGnRecu: 0,
        tauxGlobalRealisationObjectif: 0,
        tauxGlobalRealisationPrevision: 0,
        pourcentageGlobalGnTransforme: 0,
        pourcentageGlobalGazTorche: 0,
        pourcentageGlobalAutoconsommationNette: 0
      },
      totauxAutoconsommation: {
        totalAutoconsommationGlobale: 0,
        totalAutoconsommationNette: 0,
        totalGazTorche: 0,
        totalGazTorcheInterne: 0,
        totalGazTorcheExterne: 0,
        tauxGlobalAutoconsommationGlobale: 0,
        tauxGlobalAutoconsommationNette: 0,
        tauxGlobalGazTorche: 0,
        pourcentageGazTorcheInterne: 0,
        pourcentageGazTorcheExterne: 0
      },
      nombreUnitesActives: 0,
      nombreUnitesArretTotal: 0,
      nombreUnitesMaintenance: 0
    };
  }

  /**
   * Données mockées pour la réalisation (en attendant l'endpoint)
   */
  private getMockRealisationData(pmois: string): RealisationUnite[] {
    return [
      {
        uniteCode: '5X2',
        unite: 'GL1Z',
        pmois,
        objectifProductionGnl: 396665,
        previsionProductionGnl: 372000,
        reelProductionGnl: 171983.281,
        statut: 'ACTIF'
      },
      {
        uniteCode: '5X3',
        unite: 'GL2Z',
        pmois,
        objectifProductionGnl: 428500,
        previsionProductionGnl: 428500,
        reelProductionGnl: 222647.197,
        statut: 'ACTIF'
      },
      {
        uniteCode: '5X1',
        unite: 'GL3Z',
        pmois,
        objectifProductionGnl: 847.906,
        previsionProductionGnl: 848,
        reelProductionGnl: 132665.039,
        statut: 'ACTIF'
      },
      {
        uniteCode: '5X8',
        unite: 'GL4Z',
        pmois,
        objectifProductionGnl: 805138.903,
        previsionProductionGnl: 0,
        reelProductionGnl: 0,
        statut: 'ARRET_TOTAL'
      }
    ];
  }

  /**
   * Données mockées pour l'évolution mensuelle
   */
  private getMockEvolutionMensuelle(): EvolutionMensuelle[] {
    return [
      { mois: '2024-07', autoconsommationNette: 15420, gazTorche: 8950, tauxAutoconsommationNette: 12.5, tauxGazTorche: 7.2 },
      { mois: '2024-08', autoconsommationNette: 16230, gazTorche: 9120, tauxAutoconsommationNette: 13.1, tauxGazTorche: 7.4 },
      { mois: '2024-09', autoconsommationNette: 15890, gazTorche: 8760, tauxAutoconsommationNette: 12.8, tauxGazTorche: 7.0 },
      { mois: '2024-10', autoconsommationNette: 16450, gazTorche: 9340, tauxAutoconsommationNette: 13.3, tauxGazTorche: 7.6 },
      { mois: '2024-11', autoconsommationNette: 15980, gazTorche: 8890, tauxAutoconsommationNette: 12.9, tauxGazTorche: 7.1 },
      { mois: '2024-12', autoconsommationNette: 16780, gazTorche: 9560, tauxAutoconsommationNette: 13.6, tauxGazTorche: 7.8 }
    ];
  }

  /**
   * Données mockées pour les unités
   */
  private getMockUnites(): ListUnite[] {
    return [
      {
        id: 1,
        codeUnite: '5X2',
        unite: 'GL1Z',
        pmois: new Date('2024-12-01'),
        tauxAcObj: 19.0,
        tauxAcDesign: 14.8
      },
      {
        id: 2,
        codeUnite: '5X3',
        unite: 'GL2Z',
        pmois: new Date('2024-12-01'),
        tauxAcObj: 18.5,
        tauxAcDesign: 15.2
      },
      {
        id: 3,
        codeUnite: '5X1',
        unite: 'GL3Z',
        pmois: new Date('2024-12-01'),
        tauxAcObj: 20.0,
        tauxAcDesign: 16.0
      },
      {
        id: 4,
        codeUnite: '5X8',
        unite: 'GL4Z',
        pmois: new Date('2024-12-01'),
        tauxAcObj: 17.5,
        tauxAcDesign: 13.5
      }
    ];
  }

  /**
   * Données mockées pour l'autoconsommation
   */
  private getMockAutoConsData(): AutoConsMens[] {
    return [
      {
        IdAuto: 1,
        unite: '5X2',
        pmois: '01122024',
        receptionGnMois: 120000,
        transformeGnMois: 100000,
        autoConsMoisNet: 15000,
        gazTorcheeMois: 5000,
        tauxAutoConsMoisGlobal: 16.7,
        tauxGazTorcheeMois: 4.2
      },
      {
        IdAuto: 2,
        unite: '5X3',
        pmois: '01122024',
        receptionGnMois: 135000,
        transformeGnMois: 115000,
        autoConsMoisNet: 16500,
        gazTorcheeMois: 3500,
        tauxAutoConsMoisGlobal: 14.8,
        tauxGazTorcheeMois: 2.6
      },
      {
        IdAuto: 3,
        unite: '5X1',
        pmois: '01122024',
        receptionGnMois: 98000,
        transformeGnMois: 82000,
        autoConsMoisNet: 12000,
        gazTorcheeMois: 4000,
        tauxAutoConsMoisGlobal: 16.3,
        tauxGazTorcheeMois: 4.1
      }
    ];
  }

  /**
   * Données mockées pour le gaz torché
   */
  private getMockGazTorchesData(): GazTorcheeParCauseTorchage[] {
    return [
      {
        id: 1,
        unite: '5X2',
        pmois: '01122024',
        codeCauseTorchage: 'I',
        libCauseTorchage: 'Interne',
        quantiteGazTorchee: 3000
      },
      {
        id: 2,
        unite: '5X2',
        pmois: '01122024',
        codeCauseTorchage: 'E',
        libCauseTorchage: 'Externe',
        quantiteGazTorchee: 2000
      },
      {
        id: 3,
        unite: '5X3',
        pmois: '01122024',
        codeCauseTorchage: 'I',
        libCauseTorchage: 'Interne',
        quantiteGazTorchee: 2100
      },
      {
        id: 4,
        unite: '5X3',
        pmois: '01122024',
        codeCauseTorchage: 'E',
        libCauseTorchage: 'Externe',
        quantiteGazTorchee: 1400
      },
      {
        id: 5,
        unite: '5X1',
        pmois: '01122024',
        codeCauseTorchage: 'I',
        libCauseTorchage: 'Interne',
        quantiteGazTorchee: 2500
      },
      {
        id: 6,
        unite: '5X1',
        pmois: '01122024',
        codeCauseTorchage: 'E',
        libCauseTorchage: 'Externe',
        quantiteGazTorchee: 1500
      }
    ];
  }
}
