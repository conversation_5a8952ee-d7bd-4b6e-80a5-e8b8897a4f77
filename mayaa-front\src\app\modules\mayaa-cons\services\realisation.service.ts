import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';

import {
  DonneesRealisation,
  RealisationUnite,
  TotauxRealisation,
  EvolutionProduction,
  StatistiquesGenerales
} from '../../../model/Realisation';

@Injectable({
  providedIn: 'root'
})
export class RealisationService {
  private readonly baseUrl = 'http://localhost:8889/api';

  constructor(private http: HttpClient) {}

  /**
   * Récupère les données de réalisation consolidées pour une période donnée
   */
  getDonneesRealisation(periode: string): Observable<DonneesRealisation> {
    const url = `${this.baseUrl}/realisation/consolide`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<any>(url, { params }).pipe(
      map(response => this.transformerDonneesRealisation(response, periode)),
      catchError(error => {
        console.error('Erreur lors de la récupération des données de réalisation:', error);
        return of(this.getDonneesMockees(periode));
      })
    );
  }

  /**
   * Récupère les statistiques générales pour une période donnée
   */
  getStatistiquesGenerales(periode: string): Observable<StatistiquesGenerales> {
    const url = `${this.baseUrl}/realisation/statistiques`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<StatistiquesGenerales>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération des statistiques:', error);
        return of({
          nombreUnitesActives: 3,
          nombreUnitesArretTotal: 1,
          nombreUnitesMaintenance: 0,
          nombreTotalUnites: 4
        });
      })
    );
  }

  /**
   * Récupère les données de production par unité
   */
  getProductionParUnite(periode: string): Observable<RealisationUnite[]> {
    const url = `${this.baseUrl}/realisation/production-unites`;
    const params = new HttpParams().set('periode', periode);

    return this.http.get<RealisationUnite[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de la production par unité:', error);
        return of([]);
      })
    );
  }

  /**
   * Récupère l'évolution mensuelle de la production
   */
  getEvolutionProduction(periode: string, nombreMois: number = 6): Observable<EvolutionProduction[]> {
    const url = `${this.baseUrl}/realisation/evolution`;
    const params = new HttpParams()
      .set('periode', periode)
      .set('nombreMois', nombreMois.toString());

    return this.http.get<EvolutionProduction[]>(url, { params }).pipe(
      catchError(error => {
        console.error('Erreur lors de la récupération de l\'évolution:', error);
        return of(this.getEvolutionMockee());
      })
    );
  }

  /**
   * Transforme les données de l'API en format attendu par le frontend
   */
  private transformerDonneesRealisation(response: any, periode: string): DonneesRealisation {
    console.log('=== TRANSFORMATION DONNEES REALISATION ===', response);

    // L'API retourne déjà la bonne structure, on fait juste une conversion de types
    return {
      periode: response.periode || periode,
      derniereMiseAJour: new Date(response.derniereMiseAJour || Date.now()),
      statistiques: response.statistiques || {
        nombreUnitesActives: 0,
        nombreUnitesArretTotal: 0,
        nombreUnitesMaintenance: 0,
        nombreTotalUnites: 0
      },
      unites: response.unites || [],
      totaux: response.totaux || {
        totalGnRecu: 0,
        totalGnTransforme: 0,
        pourcentageGlobalGnTransforme: 0,
        totalProductionReelle: 0,
        totalObjectif: 0,
        totalPrevision: 0,
        tauxGlobalRealisationObjectif: 0
      },
      evolution: response.evolution || []
    };
  }

  /**
   * Retourne des données mockées pour les tests
   */
  private getDonneesMockees(periode: string): DonneesRealisation {
    return {
      periode,
      derniereMiseAJour: new Date(),
      statistiques: {
        nombreUnitesActives: 3,
        nombreUnitesArretTotal: 1,
        nombreUnitesMaintenance: 0,
        nombreTotalUnites: 4
      },
      unites: [
        {
          uniteCode: 'U1',
          unite: 'Unité 1',
          statut: 'ACTIF',
          productionReelle: 32000,
          objectifProduction: 35000,
          previsionProduction: 33000,
          ecartObjectif: -8.6,
          tauxRealisationObjectif: 91.4,
          gnRecu: 50000,
          gnTransforme: 40000,
          pourcentageGnTransforme: 80.0
        },
        {
          uniteCode: 'U2',
          unite: 'Unité 2',
          statut: 'ACTIF',
          productionReelle: 28000,
          objectifProduction: 30000,
          previsionProduction: 29000,
          ecartObjectif: -6.7,
          tauxRealisationObjectif: 93.3,
          gnRecu: 45000,
          gnTransforme: 36000,
          pourcentageGnTransforme: 80.0
        }
      ],
      totaux: {
        totalGnRecu: 150000,
        totalGnTransforme: 120000,
        pourcentageGlobalGnTransforme: 80.0,
        totalProductionReelle: 95000,
        totalObjectif: 100000,
        totalPrevision: 98000,
        tauxGlobalRealisationObjectif: 95.0
      },
      evolution: this.getEvolutionMockee()
    };
  }

  /**
   * Retourne des données d'évolution mockées
   */
  private getEvolutionMockee(): EvolutionProduction[] {
    return [
      { mois: 'Juil 2024', production: 85000, objectif: 90000, tauxRealisation: 94.4 },
      { mois: 'Août 2024', production: 88000, objectif: 92000, tauxRealisation: 95.7 },
      { mois: 'Sept 2024', production: 90000, objectif: 95000, tauxRealisation: 94.7 },
      { mois: 'Oct 2024', production: 92000, objectif: 96000, tauxRealisation: 95.8 },
      { mois: 'Nov 2024', production: 90000, objectif: 95000, tauxRealisation: 94.7 },
      { mois: 'Déc 2024', production: 95000, objectif: 100000, tauxRealisation: 95.0 }
    ];
  }
}
