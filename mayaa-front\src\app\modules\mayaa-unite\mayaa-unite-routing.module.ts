import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BilanComponent } from './pages/bilan/bilan.component';

import { GazTorchesDetailComponent } from './pages/gaz-torches-detail/gaz-torches-detail.component';
import { IndicateursPerformanceComponent } from './pages/indicateurs-performance/indicateurs-performance.component';
import { AnalyseExcesComponent } from './pages/analyse-exces/analyse-exces.component';
import { AnalyseExcesGazTorcheComponent } from './pages/analyse-exces-gaz-torche/analyse-exces-gaz-torche.component';
import { EvolutionCausesComponent } from './pages/evolution-causes/evolution-causes.component';
import { SyntheseArretsComponent } from './pages/synthese-arrets/synthese-arrets.component';
import { RepartitionSiegeArretsComponent } from './pages/repartition-siege-arrets/repartition-siege-arrets.component';
import { RepartitionCauseArretsComponent } from './pages/repartition-cause-arrets/repartition-cause-arrets.component';
import { SituationTrainsArretsComponent } from './pages/situation-trains-arrets/situation-trains-arrets.component';

const routes: Routes = [
  {
    path: 'bilan',
    data: {
      breadcrumb: 'Dashboard',
      //    permissions: ['$operateur.da.lister'],
      //  operateurChoosable: true,
    },

    component: BilanComponent,
    //    canActivate: [AuthGuard, OperateurChoosableGuard],
  },
  {
    path: 'performance',
    component: IndicateursPerformanceComponent,
  },
  {
    path: 'analyse-exces',
    component: AnalyseExcesComponent,
    data: {
      breadcrumb: 'Analyse Excès Autoconsommation - Energie interne et fuites',
    },
  },

  {
    path: 'analyse-exces-gt',
    component: AnalyseExcesGazTorcheComponent,
    data: {
      breadcrumb: 'Analyse Excès Autoconsommation - Gaz Torché',
    },
  },
  {
    path: 'evolution-causes',
    component: EvolutionCausesComponent,
    data: {
      breadcrumb: 'Évolution des Causes - Gaz Torché par Train',
    },
  },
  {
    path: 'arrets/synthese',
    component: SyntheseArretsComponent,
    data: {
      breadcrumb: 'Synthèse des arrêts - MAP, AC nette, GT par trains',
    },
  },
  {
    path: 'arrets/repartition-siege',
    component: RepartitionSiegeArretsComponent,
    data: {
      breadcrumb: 'Répartition des arrêts par siège internes/externes',
    },
  },
  {
    path: 'arrets/repartition-cause',
    component: RepartitionCauseArretsComponent,
    data: {
      breadcrumb: 'Répartition des arrêts par causes internes/externes',
    },
  },
  {
    path: 'arrets/situation-trains',
    component: SituationTrainsArretsComponent,
    data: {
      breadcrumb: 'Situation des Trains (arrêts)',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MayaaUniteRoutingModule { }
