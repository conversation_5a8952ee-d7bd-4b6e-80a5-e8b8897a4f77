import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabViewModule } from 'primeng/tabview';
import { MayaaUniteRoutingModule } from './mayaa-unite-routing.module';
import { BilanComponent } from './pages/bilan/bilan.component';
import { FloatLabelModule } from 'primeng/floatlabel';
import { InputTextModule } from 'primeng/inputtext';
import { FormsModule } from '@angular/forms';
import { InputNumberModule } from 'primeng/inputnumber';
import { ChartModule } from 'primeng/chart';
import { ButtonModule } from 'primeng/button';
import { TableModule } from 'primeng/table';
import { ConsommationDetailComponent } from './pages/consommation-detail/consommation-detail.component';
import { GazTorchesDetailComponent } from './pages/gaz-torches-detail/gaz-torches-detail.component';
import { IndicateursPerformanceComponent } from './pages/indicateurs-performance/indicateurs-performance.component';
import { AnalyseExcesComponent } from './pages/analyse-exces/analyse-exces.component';
import { AnalyseExcesGazTorcheComponent } from './pages/analyse-exces-gaz-torche/analyse-exces-gaz-torche.component';
import { EvolutionCausesComponent } from './pages/evolution-causes/evolution-causes.component';
import { SyntheseArretsComponent } from './pages/synthese-arrets/synthese-arrets.component';
import { RepartitionSiegeArretsComponent } from './pages/repartition-siege-arrets/repartition-siege-arrets.component';
import { RepartitionCauseArretsComponent } from './pages/repartition-cause-arrets/repartition-cause-arrets.component';
import { SituationTrainsArretsComponent } from './pages/situation-trains-arrets/situation-trains-arrets.component';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressBarModule } from 'primeng/progressbar';
import { CardModule } from 'primeng/card';
import { AccordionModule } from 'primeng/accordion';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { MessageModule } from 'primeng/message';
import { BadgeModule } from 'primeng/badge';
import { DropdownModule } from 'primeng/dropdown';
import { SelectButtonModule } from 'primeng/selectbutton';
@NgModule({
  declarations: [
    BilanComponent,
    ConsommationDetailComponent,
    GazTorchesDetailComponent,
    IndicateursPerformanceComponent,
    AnalyseExcesComponent,
    AnalyseExcesGazTorcheComponent,
    EvolutionCausesComponent,
    SyntheseArretsComponent,
    RepartitionSiegeArretsComponent,
    RepartitionCauseArretsComponent,
    SituationTrainsArretsComponent,
  ],
  imports: [
    CommonModule,
    MayaaUniteRoutingModule,
    TabViewModule,
    FloatLabelModule,
    InputTextModule,
    FormsModule,
    InputNumberModule,
    ChartModule,
    ButtonModule,
    TableModule,
    TooltipModule,
    ProgressBarModule,
    CardModule,
    AccordionModule,
    ProgressSpinnerModule,
    MessageModule,
    BadgeModule,
    DropdownModule,
    SelectButtonModule,
  ],
})
export class MayaaUniteModule {}
