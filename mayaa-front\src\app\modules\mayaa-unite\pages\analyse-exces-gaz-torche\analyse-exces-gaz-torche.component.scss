.analyse-exces-gaz-torche-container {
  padding: 1rem;
  
  .page-header {
    margin-bottom: 1.5rem;
    
    h2 {
      color: #2c3e50;
      font-weight: 600;
      margin: 0;
      font-size: 1.5rem;
    }
  }

  .stats-container {
    margin-bottom: 2rem;
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      
      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        
        .stat-value {
          font-size: 2rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }
        
        .stat-label {
          font-size: 0.9rem;
          opacity: 0.9;
        }
      }
    }
  }

  .content-container {
    .trains-container {
      .train-section {
        margin-bottom: 2.5rem;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        .train-header {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;
          padding: 1rem 1.5rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .train-title {
            margin: 0;
            font-size: 1.25rem;
            font-weight: 600;
          }
          
          .train-stats {
            display: flex;
            gap: 1.5rem;
            
            .train-stat {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.9rem;
              
              i {
                font-size: 1rem;
              }
            }
          }
        }
        
        .table-container {
          .train-table {
            border: none;
            
            .p-datatable-thead > tr > th {
              background: #f8f9fa;
              color: #495057;
              font-weight: 600;
              padding: 0.75rem;
              border-bottom: 2px solid #dee2e6;
            }
            
            .p-datatable-tbody > tr {
              &:hover {
                background: #f8f9fa;
              }
              
              td {
                padding: 0.75rem;
                border-bottom: 1px solid #dee2e6;
                
                &.text-right {
                  text-align: right;
                }
                
                &.font-weight-bold {
                  font-weight: 600;
                  color: #2c3e50;
                }
              }
            }
          }
        }
      }
    }
    
    .loading-container {
      text-align: center;
      padding: 3rem;
      
      p {
        margin-top: 1rem;
        color: #6c757d;
        font-size: 1.1rem;
      }
    }
    
    .no-data-message {
      text-align: center;
      padding: 2rem;
    }
  }

  .summary-container {
    margin-top: 2rem;
    
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      
      .summary-card {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        h4 {
          margin: 0 0 1rem 0;
          color: #2c3e50;
          font-size: 1.1rem;
          font-weight: 600;
        }
        
        .summary-items {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          
          .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .summary-count {
              background: #e9ecef;
              color: #495057;
              padding: 0.25rem 0.75rem;
              border-radius: 1rem;
              font-weight: 600;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
  }

  // Badges pour les états
  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    
    &.status-en-cours {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    &.status-terminé {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    &.status-planifié {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    &.status-annulé {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  }

  // Badges pour la criticité
  .criticality-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    
    &.criticality-critique {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    &.criticality-majeur {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    &.criticality-mineur {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 0.5rem;
    
    .page-header h2 {
      font-size: 1.25rem;
    }
    
    .stats-container .stats-grid {
      grid-template-columns: 1fr;
    }
    
    .trains-container .train-section .train-header {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
      
      .train-stats {
        justify-content: center;
      }
    }
    
    .summary-container .summary-grid {
      grid-template-columns: 1fr;
    }
  }
}

// Amélioration globale des tableaux
::ng-deep {
  .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.5rem 0.75rem;
  }
  
  .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.5rem 0.75rem;
  }
  
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
    background: #f8f9fa;
  }
}
