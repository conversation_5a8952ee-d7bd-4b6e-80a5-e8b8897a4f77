import { Component, OnInit } from '@angular/core';
import { AnalyseExcesGazTorcheService } from '../../services/analyse-exces-gaz-torche.service';
import { AnalyseExcesGazTorche, TableColumn } from '../../../../model/analyse-exces-gaz-torche.model';

@Component({
  selector: 'app-analyse-exces-gaz-torche',
  templateUrl: './analyse-exces-gaz-torche.component.html',
  styleUrls: ['./analyse-exces-gaz-torche.component.scss']
})
export class AnalyseExcesGazTorcheComponent implements OnInit {

  // Données pour les trains (gaz torché)
  gazTorcheData: AnalyseExcesGazTorche[] = [];

  // Colonnes pour le tableau
  gazTorcheColumns: TableColumn[] = [
    { field: 'codeAc', header: 'CODE AC', sortable: true },
    { field: 'problemeSpecifique', header: 'PROBLÈME SPÉCIFIQUE', sortable: true },
    { field: 'intitule', header: 'INTITULÉ', sortable: true },
    { field: 'ac', header: 'AC (10³ CM³ GN)', sortable: true },
    { field: 'classeCauses', header: 'CLASSE CAUSES', sortable: true },
    { field: 'causes', header: 'CAUSES', sortable: false },
    { field: 'actions', header: 'ACTIONS', sortable: false },
    { field: 'classes', header: 'CLASSES', sortable: true },
    { field: 'etat', header: 'ÉTAT', sortable: true },
    { field: 'numero', header: 'N°', sortable: true }
  ];

  loading = false;

  // Paramètres pour les appels API (à connecter avec les contrôles de l'interface)
  currentUnite = 'GL1K'; // Valeur par défaut, à récupérer depuis le contexte
  currentMois = '2024-01'; // Valeur par défaut, à récupérer depuis le contexte

  constructor(private analyseExcesGazTorcheService: AnalyseExcesGazTorcheService) { }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loading = true;

    // Utilisation du service pour charger les données
    this.analyseExcesGazTorcheService.getGazTorcheData(this.currentUnite, this.currentMois)
      .subscribe({
        next: (data) => {
          this.gazTorcheData = data;
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données gaz torché:', error);
          this.loading = false;
          // TODO: Afficher un message d'erreur à l'utilisateur
        }
      });
  }

  // Méthodes pour la gestion des trains
  getUniqueTrains(): string[] {
    const trains = this.gazTorcheData.map(item => item.trainName);
    return [...new Set(trains)].sort();
  }

  getTrainData(trainName: string): AnalyseExcesGazTorche[] {
    return this.gazTorcheData.filter(item => item.trainName === trainName);
  }

  // Méthode pour rafraîchir les données quand l'unité ou le mois change
  refreshData(unite?: string, mois?: string): void {
    if (unite) this.currentUnite = unite;
    if (mois) this.currentMois = mois;
    this.loadData();
  }

  // Méthode pour calculer le total AC par train
  getTrainTotalAC(trainName: string): number {
    const trainData = this.getTrainData(trainName);
    return trainData.reduce((total, item) => total + item.ac, 0);
  }

  // Méthode pour obtenir le nombre d'éléments par train
  getTrainItemCount(trainName: string): number {
    return this.getTrainData(trainName).length;
  }

  // Méthode pour obtenir les statistiques globales
  getTotalAC(): number {
    return this.gazTorcheData.reduce((total, item) => total + item.ac, 0);
  }

  getTotalItems(): number {
    return this.gazTorcheData.length;
  }

  // Méthode pour obtenir la répartition par état
  getStatusDistribution(): { [key: string]: number } {
    const distribution: { [key: string]: number } = {};
    this.gazTorcheData.forEach(item => {
      distribution[item.etat] = (distribution[item.etat] || 0) + 1;
    });
    return distribution;
  }

  // Méthode pour obtenir la répartition par classe de criticité
  getCriticalityDistribution(): { [key: string]: number } {
    const distribution: { [key: string]: number } = {};
    this.gazTorcheData.forEach(item => {
      distribution[item.classes] = (distribution[item.classes] || 0) + 1;
    });
    return distribution;
  }
}
