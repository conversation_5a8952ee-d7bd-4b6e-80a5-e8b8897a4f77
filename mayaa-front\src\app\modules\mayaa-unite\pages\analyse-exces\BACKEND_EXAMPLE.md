# Exemple d'implémentation Backend

## Contrôleur Java (Spring Boot)

```java
@RestController
@RequestMapping("/api/mayaa-unite")
@CrossOrigin(origins = "*")
public class AnalyseExcesController {

    @Autowired
    private AnalyseExcesService analyseExcesService;

    /**
     * Récup<PERSON> toutes les données d'analyse d'excès
     */
    @GetMapping("/analyse-exces")
    public ResponseEntity<AnalyseExcesResponse> getAnalyseExces(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            AnalyseExcesResponse response = analyseExcesService.getAnalyseExcesData(unite, mois);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère uniquement les données d'utilités
     */
    @GetMapping("/analyse-exces/utilites")
    public ResponseEntity<List<AnalyseExcesUtilite>> getUtilitesData(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            List<AnalyseExcesUtilite> utilites = analyseExcesService.getUtilitesData(unite, mois);
            return ResponseEntity.ok(utilites);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Récupère uniquement les données de trains
     */
    @GetMapping("/analyse-exces/trains")
    public ResponseEntity<List<AnalyseExcesTrain>> getTrainsData(
            @RequestParam String unite,
            @RequestParam String mois) {
        
        try {
            List<AnalyseExcesTrain> trains = analyseExcesService.getTrainsData(unite, mois);
            return ResponseEntity.ok(trains);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
```

## Modèles Java (DTOs)

```java
// AnalyseExcesUtilite.java
public class AnalyseExcesUtilite {
    private String codeAc;
    private String problemeSpecifique;
    private String intitule;
    private Double ac; // 10³ CM³ GN
    private String classeCauses;
    private String causes;
    private String actions;
    private String classes;
    private String etat;
    private String numero;
    
    // Constructeurs, getters et setters
}

// AnalyseExcesTrain.java
public class AnalyseExcesTrain {
    private String codeAc;
    private String problemeSpecifique;
    private String intitule;
    private Double ac; // 10³ CM³ GN
    private String classeCauses;
    private String causes;
    private String actions;
    private String classes;
    private String etat;
    private String numero;
    private String trainName;
    
    // Constructeurs, getters et setters
}

// AnalyseExcesResponse.java
public class AnalyseExcesResponse {
    private List<AnalyseExcesUtilite> utilites;
    private List<AnalyseExcesTrain> trains;
    
    // Constructeurs, getters et setters
}
```

## Service Java

```java
@Service
public class AnalyseExcesService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public AnalyseExcesResponse getAnalyseExcesData(String unite, String mois) {
        AnalyseExcesResponse response = new AnalyseExcesResponse();
        response.setUtilites(getUtilitesData(unite, mois));
        response.setTrains(getTrainsData(unite, mois));
        return response;
    }

    public List<AnalyseExcesUtilite> getUtilitesData(String unite, String mois) {
        String sql = """
            SELECT 
                CODE_AC,
                PROBLEME_SPECIFIQUE,
                INTITULE,
                AC,
                CLASSE_CAUSES,
                CAUSES,
                ACTIONS,
                CLASSES,
                ETAT,
                NUMERO
            FROM ANALYSE_EXCES_AUTOCONSOMMATION_UTILITES
            WHERE UNITE = ? AND MOIS = ?
            ORDER BY AC DESC
        """;
        
        return jdbcTemplate.query(sql, new Object[]{unite, mois}, 
            (rs, rowNum) -> {
                AnalyseExcesUtilite utilite = new AnalyseExcesUtilite();
                utilite.setCodeAc(rs.getString("CODE_AC"));
                utilite.setProblemeSpecifique(rs.getString("PROBLEME_SPECIFIQUE"));
                utilite.setIntitule(rs.getString("INTITULE"));
                utilite.setAc(rs.getDouble("AC"));
                utilite.setClasseCauses(rs.getString("CLASSE_CAUSES"));
                utilite.setCauses(rs.getString("CAUSES"));
                utilite.setActions(rs.getString("ACTIONS"));
                utilite.setClasses(rs.getString("CLASSES"));
                utilite.setEtat(rs.getString("ETAT"));
                utilite.setNumero(rs.getString("NUMERO"));
                return utilite;
            });
    }

    public List<AnalyseExcesTrain> getTrainsData(String unite, String mois) {
        String sql = """
            SELECT 
                CODE_AC,
                PROBLEME_SPECIFIQUE,
                INTITULE,
                AC,
                CLASSE_CAUSES,
                CAUSES,
                ACTIONS,
                CLASSES,
                ETAT,
                NUMERO,
                TRAIN_NAME
            FROM ANALYSE_EXCES_AUTOCONSOMMATION_TRAINS
            WHERE UNITE = ? AND MOIS = ?
            ORDER BY TRAIN_NAME, AC DESC
        """;
        
        return jdbcTemplate.query(sql, new Object[]{unite, mois}, 
            (rs, rowNum) -> {
                AnalyseExcesTrain train = new AnalyseExcesTrain();
                train.setCodeAc(rs.getString("CODE_AC"));
                train.setProblemeSpecifique(rs.getString("PROBLEME_SPECIFIQUE"));
                train.setIntitule(rs.getString("INTITULE"));
                train.setAc(rs.getDouble("AC"));
                train.setClasseCauses(rs.getString("CLASSE_CAUSES"));
                train.setCauses(rs.getString("CAUSES"));
                train.setActions(rs.getString("ACTIONS"));
                train.setClasses(rs.getString("CLASSES"));
                train.setEtat(rs.getString("ETAT"));
                train.setNumero(rs.getString("NUMERO"));
                train.setTrainName(rs.getString("TRAIN_NAME"));
                return train;
            });
    }
}
```

## Structure de base de données suggérée

```sql
-- Table pour les utilités
CREATE TABLE ANALYSE_EXCES_AUTOCONSOMMATION_UTILITES (
    ID NUMBER PRIMARY KEY,
    UNITE VARCHAR2(10) NOT NULL,
    MOIS VARCHAR2(7) NOT NULL, -- Format YYYY-MM
    CODE_AC VARCHAR2(20),
    PROBLEME_SPECIFIQUE VARCHAR2(500),
    INTITULE VARCHAR2(200),
    AC NUMBER(10,2), -- 10³ CM³ GN
    CLASSE_CAUSES VARCHAR2(100),
    CAUSES VARCHAR2(1000),
    ACTIONS VARCHAR2(1000),
    CLASSES VARCHAR2(50),
    ETAT VARCHAR2(50),
    NUMERO VARCHAR2(20),
    CREATED_DATE DATE DEFAULT SYSDATE,
    UPDATED_DATE DATE DEFAULT SYSDATE
);

-- Table pour les trains
CREATE TABLE ANALYSE_EXCES_AUTOCONSOMMATION_TRAINS (
    ID NUMBER PRIMARY KEY,
    UNITE VARCHAR2(10) NOT NULL,
    MOIS VARCHAR2(7) NOT NULL, -- Format YYYY-MM
    CODE_AC VARCHAR2(20),
    PROBLEME_SPECIFIQUE VARCHAR2(500),
    INTITULE VARCHAR2(200),
    AC NUMBER(10,2), -- 10³ CM³ GN
    CLASSE_CAUSES VARCHAR2(100),
    CAUSES VARCHAR2(1000),
    ACTIONS VARCHAR2(1000),
    CLASSES VARCHAR2(50),
    ETAT VARCHAR2(50),
    NUMERO VARCHAR2(20),
    TRAIN_NAME VARCHAR2(50),
    CREATED_DATE DATE DEFAULT SYSDATE,
    UPDATED_DATE DATE DEFAULT SYSDATE
);

-- Index pour les performances
CREATE INDEX IDX_ANALYSE_EXCES_UTIL_UNITE_MOIS ON ANALYSE_EXCES_AUTOCONSOMMATION_UTILITES(UNITE, MOIS);
CREATE INDEX IDX_ANALYSE_EXCES_TRAIN_UNITE_MOIS ON ANALYSE_EXCES_AUTOCONSOMMATION_TRAINS(UNITE, MOIS);
```

## Configuration CORS (si nécessaire)

```java
@Configuration
@EnableWebMvc
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("http://localhost:4200", "http://localhost:56369")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true);
    }
}
```
