# Analyse de l'excès de l'autoconsommation

## Description

Ce composant affiche l'analyse de l'excès de l'autoconsommation (Energie interne et fuites) avec deux onglets :
- **Utilités** : Analyse des problèmes d'autoconsommation liés aux utilités
- **Trains** : Analyse des problèmes d'autoconsommation liés aux trains, organisés par train

## Structure des fichiers

```
analyse-exces/
├── analyse-exces.component.ts      # Composant principal
├── analyse-exces.component.html    # Template avec TabView
├── analyse-exces.component.scss    # Styles du composant
└── README.md                       # Documentation
```

## Fonctionnalités

### Onglet Utilités
- Tableau paginé avec tri sur les colonnes
- Affichage des données d'analyse d'excès pour les utilités
- Badges colorés pour les états (En cours, Terminé, Planifié, Annul<PERSON>)

### Onglet Trains
- Organisation des données par train
- Tableaux séparés pour chaque train
- Même structure de données que les utilités mais avec identification du train

## Colonnes des tableaux

| Colonne | Description | Type |
|---------|-------------|------|
| CODE AC | Code d'autoconsommation | string |
| PROBLÈME SPÉCIFIQUE | Description du problème | string |
| INTITULÉ | Intitulé de l'élément | string |
| AC (10³ CM³ GN) | Quantité d'autoconsommation | number |
| CLASSE CAUSES | Classification des causes | string |
| CAUSES | Description des causes | string |
| ACTIONS | Actions correctives | string |
| CLASSES | Classe de criticité | string |
| ÉTAT | État actuel | string |
| N° | Numéro de référence | string |

## Intégration API

### Service AnalyseExcesService

Le service `AnalyseExcesService` est prêt pour l'intégration avec l'API backend :

```typescript
// Méthodes disponibles
getAnalyseExcesData(unite: string, mois: string): Observable<AnalyseExcesResponse>
getUtilitesData(unite: string, mois: string): Observable<AnalyseExcesUtilite[]>
getTrainsData(unite: string, mois: string): Observable<AnalyseExcesTrain[]>
```

### Endpoints API à implémenter

```
GET /api/mayaa-unite/analyse-exces?unite={unite}&mois={mois}
GET /api/mayaa-unite/analyse-exces/utilites?unite={unite}&mois={mois}
GET /api/mayaa-unite/analyse-exces/trains?unite={unite}&mois={mois}
```

## Modèles de données

Les interfaces TypeScript sont définies dans `models/analyse-exces.model.ts` :

- `AnalyseExcesUtilite` : Structure pour les données d'utilités
- `AnalyseExcesTrain` : Structure pour les données de trains
- `AnalyseExcesResponse` : Réponse complète de l'API
- `TableColumn` : Configuration des colonnes de tableau

## Styles et thème

Le composant utilise :
- **PrimeNG** pour les composants UI (TabView, Table, Paginator)
- **Styles personnalisés** pour l'apparence et la responsivité
- **Badges colorés** pour les états
- **Design moderne** avec bordures arrondies et ombres

## États possibles

- **En cours** : Badge jaune
- **Terminé** : Badge vert
- **Planifié** : Badge bleu
- **Annulé** : Badge rouge

## Données mock

Le composant utilise actuellement des données mock pour le développement. Ces données seront remplacées par les appels API réels une fois que :

1. La vue backend sera créée
2. Le contrôleur sera implémenté
3. Les endpoints API seront disponibles

## Prochaines étapes

1. **Backend** :
   - Créer la vue `ANALYSE_EXCES_AUTOCONSOMMATION`
   - Implémenter le contrôleur `AnalyseExcesController`
   - Définir les endpoints API

2. **Frontend** :
   - Connecter aux contrôles de sélection d'unité/mois
   - Implémenter la gestion d'erreurs
   - Ajouter des indicateurs de chargement
   - Tests unitaires

3. **Intégration** :
   - Remplacer les données mock par les appels API
   - Tester avec des données réelles
   - Optimiser les performances

## Utilisation

Le composant est accessible via la route `/mayaa-unite/analyse-exces` et s'intègre automatiquement dans le module `MayaaUniteModule`.
