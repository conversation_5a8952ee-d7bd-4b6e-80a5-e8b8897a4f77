<div class="analyse-exces-container">
  <div class="page-header">
    <h2>Analyse de l'excès de l'autoconsommation (Energie interne et fuites)</h2>
  </div>

  <p-tabView (onChange)="onTabChange($event)" styleClass="custom-tabview">
    
    <!-- Onglet Utilités -->
    <p-tabPanel header="Utilités" leftIcon="pi pi-cog">
      <div class="tab-content">
        <div class="section-header">
          <h3>4. Analyse de l'excès de l'autoconsommation (Energie interne et fuites)</h3>
          <h4>Utilités</h4>
        </div>

        <div class="table-container">
          <p-table 
            [value]="utilitesData" 
            [loading]="loading"
            [paginator]="true" 
            [rows]="10"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="Affichage de {first} à {last} sur {totalRecords} entrées"
            [rowsPerPageOptions]="[10, 25, 50]"
            styleClass="p-datatable-sm p-datatable-striped"
            responsiveLayout="scroll">
            
            <ng-template pTemplate="header">
              <tr>
                <th *ngFor="let col of utilitesColumns" 
                    [pSortableColumn]="col.field"
                    class="table-header">
                  {{ col.header }}
                  <p-sortIcon [field]="col.field"></p-sortIcon>
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-item>
              <tr>
                <td>{{ item.codeAc }}</td>
                <td>{{ item.problemeSpecifique }}</td>
                <td>{{ item.intitule }}</td>
                <td class="text-right">{{ item.ac | number:'1.1-1' }}</td>
                <td>{{ item.classeCauses }}</td>
                <td>{{ item.causes }}</td>
                <td>{{ item.actions }}</td>
                <td>{{ item.classes }}</td>
                <td>
                  <span [class]="'status-badge status-' + item.etat.toLowerCase().replace(' ', '-')">
                    {{ item.etat }}
                  </span>
                </td>
                <td>{{ item.numero }}</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td [attr.colspan]="utilitesColumns.length" class="text-center">
                  Aucune donnée disponible pour les utilités
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </p-tabPanel>

    <!-- Onglet Trains -->
    <p-tabPanel header="Trains" leftIcon="pi pi-sitemap">
      <div class="tab-content">
        <div class="section-header">
          <h3>4. Analyse de l'excès de l'autoconsommation (Energie interne et fuites)</h3>
          <h4>Trains</h4>
        </div>

        <!-- Affichage par train -->
        <div class="trains-container">
          <div *ngFor="let train of getUniqueTrains()" class="train-section">
            <h5 class="train-title">{{ train }}</h5>
            
            <div class="table-container">
              <p-table 
                [value]="getTrainData(train)" 
                [loading]="loading"
                styleClass="p-datatable-sm p-datatable-striped train-table"
                responsiveLayout="scroll">
                
                <ng-template pTemplate="header">
                  <tr>
                    <th *ngFor="let col of trainsColumns" 
                        [pSortableColumn]="col.field"
                        class="table-header">
                      {{ col.header }}
                      <p-sortIcon [field]="col.field"></p-sortIcon>
                    </th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-item>
                  <tr>
                    <td>{{ item.codeAc }}</td>
                    <td>{{ item.problemeSpecifique }}</td>
                    <td>{{ item.intitule }}</td>
                    <td class="text-right">{{ item.ac | number:'1.1-1' }}</td>
                    <td>{{ item.classeCauses }}</td>
                    <td>{{ item.causes }}</td>
                    <td>{{ item.actions }}</td>
                    <td>{{ item.classes }}</td>
                    <td>
                      <span [class]="'status-badge status-' + item.etat.toLowerCase().replace(' ', '-')">
                        {{ item.etat }}
                      </span>
                    </td>
                    <td>{{ item.numero }}</td>
                  </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td [attr.colspan]="trainsColumns.length" class="text-center">
                      Aucune donnée disponible pour ce train
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>

          <div *ngIf="getUniqueTrains().length === 0" class="no-trains-message">
            <p-message severity="info" text="Aucun train disponible"></p-message>
          </div>
        </div>
      </div>
    </p-tabPanel>

  </p-tabView>
</div>
