.analyse-exces-container {
  padding: 1rem;
  
  .page-header {
    margin-bottom: 1.5rem;
    
    h2 {
      color: #2c3e50;
      font-weight: 600;
      margin: 0;
      font-size: 1.5rem;
    }
  }

  .custom-tabview {
    .p-tabview-nav {
      background: #f8f9fa;
      border-bottom: 2px solid #dee2e6;
      
      .p-tabview-nav-link {
        padding: 1rem 1.5rem;
        font-weight: 500;
        color: #495057;
        border: none;
        background: transparent;
        
        &:hover {
          background: #e9ecef;
          color: #2c3e50;
        }
        
        &.p-highlight {
          background: #007bff;
          color: white;
          border-bottom: 3px solid #0056b3;
        }
      }
    }
    
    .p-tabview-panels {
      background: white;
      border: none;
      padding: 0;
    }
  }

  .tab-content {
    padding: 1.5rem;
    
    .section-header {
      margin-bottom: 1.5rem;
      
      h3 {
        color: #2c3e50;
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 0.5rem 0;
      }
      
      h4 {
        color: #6c757d;
        font-size: 1.1rem;
        font-weight: 500;
        margin: 0;
      }
    }
  }

  .table-container {
    margin-bottom: 2rem;
    
    .p-datatable {
      border: 1px solid #dee2e6;
      border-radius: 0.375rem;
      overflow: hidden;
      
      .table-header {
        background: #f8f9fa;
        color: #495057;
        font-weight: 600;
        padding: 0.75rem;
        border-bottom: 2px solid #dee2e6;
      }
      
      .p-datatable-tbody > tr {
        &:hover {
          background: #f8f9fa;
        }
        
        td {
          padding: 0.75rem;
          border-bottom: 1px solid #dee2e6;
          
          &.text-right {
            text-align: right;
          }
          
          &.text-center {
            text-align: center;
          }
        }
      }
    }
  }

  .trains-container {
    .train-section {
      margin-bottom: 2rem;
      
      .train-title {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
        padding: 0.5rem 1rem;
        background: #e9ecef;
        border-left: 4px solid #007bff;
        border-radius: 0.25rem;
      }
      
      .train-table {
        margin-left: 1rem;
      }
    }
    
    .no-trains-message {
      text-align: center;
      padding: 2rem;
    }
  }

  .status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-transform: uppercase;
    
    &.status-en-cours {
      background: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    
    &.status-terminé {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    &.status-planifié {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    &.status-annulé {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 0.5rem;
    
    .page-header h2 {
      font-size: 1.25rem;
    }
    
    .tab-content {
      padding: 1rem;
    }
    
    .custom-tabview .p-tabview-nav .p-tabview-nav-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
    
    .trains-container .train-section .train-table {
      margin-left: 0;
    }
  }
}

// Amélioration de l'apparence des tableaux PrimeNG
::ng-deep {
  .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.5rem 0.75rem;
  }
  
  .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.5rem 0.75rem;
  }
  
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
    background: #f8f9fa;
  }
  
  .p-paginator {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 0.75rem;
  }
}
