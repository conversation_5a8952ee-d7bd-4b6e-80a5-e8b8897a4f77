import { Component, OnInit } from '@angular/core';
import { AnalyseExcesService } from '../../services/analyse-exces.service';
import { AnalyseExcesUtilite, AnalyseExcesTrain, TableColumn } from '../../../../model/analyse-exces.model';

@Component({
  selector: 'app-analyse-exces',
  templateUrl: './analyse-exces.component.html',
  styleUrls: ['./analyse-exces.component.scss']
})
export class AnalyseExcesComponent implements OnInit {

  // Données pour les utilités
  utilitesData: AnalyseExcesUtilite[] = [];

  // Données pour les trains
  trainsData: AnalyseExcesTrain[] = [];

  // Colonnes pour les tableaux
  utilitesColumns: TableColumn[] = [
    { field: 'codeAc', header: 'CODE AC', sortable: true },
    { field: 'problemeSpecifique', header: 'PROBLÈME SPÉCIFIQUE', sortable: true },
    { field: 'intitule', header: 'INTITULÉ', sortable: true },
    { field: 'ac', header: 'AC (10³ CM³ GN)', sortable: true },
    { field: 'classeCauses', header: 'CLASSE CAUSES', sortable: true },
    { field: 'causes', header: 'CAUSES', sortable: false },
    { field: 'actions', header: 'ACTIONS', sortable: false },
    { field: 'classes', header: 'CLASSES', sortable: true },
    { field: 'etat', header: 'ÉTAT', sortable: true },
    { field: 'numero', header: 'N°', sortable: true }
  ];

  trainsColumns: TableColumn[] = [
    { field: 'codeAc', header: 'CODE AC', sortable: true },
    { field: 'problemeSpecifique', header: 'PROBLÈME SPÉCIFIQUE', sortable: true },
    { field: 'intitule', header: 'INTITULÉ', sortable: true },
    { field: 'ac', header: 'AC (10³ CM³ GN)', sortable: true },
    { field: 'classeCauses', header: 'CLASSE CAUSES', sortable: true },
    { field: 'causes', header: 'CAUSES', sortable: false },
    { field: 'actions', header: 'ACTIONS', sortable: false },
    { field: 'classes', header: 'CLASSES', sortable: true },
    { field: 'etat', header: 'ÉTAT', sortable: true },
    { field: 'numero', header: 'N°', sortable: true }
  ];

  loading = false;

  // Paramètres pour les appels API (à connecter avec les contrôles de l'interface)
  currentUnite = 'GL1K'; // Valeur par défaut, à récupérer depuis le contexte
  currentMois = '2024-01'; // Valeur par défaut, à récupérer depuis le contexte

  constructor(private analyseExcesService: AnalyseExcesService) { }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loading = true;

    // Utilisation du service pour charger les données
    this.analyseExcesService.getAnalyseExcesData(this.currentUnite, this.currentMois)
      .subscribe({
        next: (data) => {
          this.utilitesData = data.utilites;
          this.trainsData = data.trains;
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données:', error);
          this.loading = false;
          // TODO: Afficher un message d'erreur à l'utilisateur
        }
      });
  }

  // Méthodes pour charger des données spécifiques
  loadUtilitesData(): void {
    this.loading = true;
    this.analyseExcesService.getUtilitesData(this.currentUnite, this.currentMois)
      .subscribe({
        next: (data) => {
          this.utilitesData = data;
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données utilités:', error);
          this.loading = false;
        }
      });
  }

  loadTrainsData(): void {
    this.loading = true;
    this.analyseExcesService.getTrainsData(this.currentUnite, this.currentMois)
      .subscribe({
        next: (data) => {
          this.trainsData = data;
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données trains:', error);
          this.loading = false;
        }
      });
  }

  // Méthode pour rafraîchir les données quand l'unité ou le mois change
  refreshData(unite?: string, mois?: string): void {
    if (unite) this.currentUnite = unite;
    if (mois) this.currentMois = mois;
    this.loadData();
  }

  onTabChange(event: any): void {
    console.log('Onglet changé:', event.index);
    // TODO: Charger les données spécifiques à l'onglet si nécessaire
  }

  // Méthodes pour la gestion des trains
  getUniqueTrains(): string[] {
    const trains = this.trainsData.map(item => item.trainName);
    return [...new Set(trains)].sort();
  }

  getTrainData(trainName: string): AnalyseExcesTrain[] {
    return this.trainsData.filter(item => item.trainName === trainName);
  }
}
