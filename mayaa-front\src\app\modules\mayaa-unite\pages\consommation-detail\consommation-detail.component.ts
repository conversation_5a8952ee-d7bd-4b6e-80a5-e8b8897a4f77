import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MayaaUniteService } from '../../services/mayaa-unite.service';
import { DetailMensuel, ConsommationJournaliere } from '../../../../model/ConsommationDetail';

@Component({
  selector: 'app-consommation-detail',
  templateUrl: './consommation-detail.component.html',
  styleUrls: ['./consommation-detail.component.scss']
})
export class ConsommationDetailComponent implements OnInit {
  chartData: any;
  chartOptions: any;
  donneesJournalieres: ConsommationJournaliere[] = [];
  cols: any[] = [];

  constructor(
    private mayaaUniteService: MayaaUniteService,
    private router: Router
  ) {}

  ngOnInit() {
    this.cols = [
      { field: 'jour', header: 'Jour' },
      { field: 'poste', header: 'Poste' },
      { field: 'cm3GN', header: 'Consommation (Cm³)' }
    ];

    this.mayaaUniteService.getConsommationDetail().subscribe(data => {
      this.prepareChartData(data);
      this.donneesJournalieres = data.donneesJournalieres as ConsommationJournaliere[];
    });
  }

  onRetour() {
    this.router.navigate(['unite/tdb']);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('fr-FR').format(value);
  }

  private prepareChartData(data: DetailMensuel) {
    this.chartData = {
      labels: data.postes?.map(p => p.poste) || [],
      datasets: [
        {
          type: 'bar',
          label: 'Consommation (Cm³)',
          data: data.postes?.map(p => p.cm3GN) || [],
          backgroundColor: '#4CAF50',
          order: 2
        },
        {
          type: 'line',
          label: 'Pourcentage GN',
          data: data.postes?.map(p => p.pourcentageGN) || [],
          borderColor: '#FFA726',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          order: 1,
          yAxisID: 'percentage'
        }
      ]
    };

    this.chartOptions = {
      plugins: {
        title: {
          display: true,
          text: `Détail Consommation Nette par Poste - ${data.mois} ${data.annee}`,
          font: { size: 16 }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function(context: any) {
              let label = context.dataset.label || '';
              if (label) {
                label += ': ';
              }
              if (context.datasetIndex === 0) {
                return label + new Intl.NumberFormat('fr-FR').format(context.raw) + ' Cm³';
              } else {
                return label + context.raw.toFixed(2) + '%';
              }
            }
          }
        }
      },
      responsive: true,
      scales: {
        x: {
          ticks: {
            color: '#495057'
          },
          grid: {
            color: '#ebedef'
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          ticks: {
            color: '#495057',
            callback: function(value: any) {
              return new Intl.NumberFormat('fr-FR').format(value) + ' Cm³';
            }
          },
          grid: {
            color: '#ebedef'
          }
        },
        percentage: {
          type: 'linear',
          display: true,
          position: 'right',
          ticks: {
            color: '#495057',
            callback: function(value: any) {
              return value + '%';
            }
          },
          grid: {
            drawOnChartArea: false
          }
        }
      }
    };
  }
} 