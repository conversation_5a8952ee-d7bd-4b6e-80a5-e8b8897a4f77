<div class="evolution-causes-container">
  <!-- En-tête avec titre et contrôles -->
  <div class="page-header">
    <div class="header-content">
      <h2 class="page-title">
        <i class="pi pi-chart-line"></i>
        Évolution des Causes - Gaz Torché par Train
      </h2>
      <div class="header-controls">
        <!-- Switch Button pour Sièges/Classes -->
        <p-selectButton
          [options]="modeOptions"
          [(ngModel)]="selectedMode"
          (onChange)="onModeChange()"
          optionLabel="label"
          optionValue="value"
          [disabled]="loading">
          <ng-template let-item>
            <i [class]="item.icon"></i>
            <span class="ml-2">{{ item.label }}</span>
          </ng-template>
        </p-selectButton>

        <!-- Dropdown des trains -->
        <p-dropdown
          [options]="trainsDisponibles"
          [(ngModel)]="selectedTrain"
          (onChange)="onTrainChange()"
          placeholder="Sélectionner un train"
          optionLabel="label"
          optionValue="value"
          [style]="{'min-width': '200px'}"
          [disabled]="loading">
          <ng-template let-train pTemplate="selectedItem">
            <div class="train-option">
              <i [class]="train.isComplexe ? 'pi pi-building' : 'pi pi-cog'"></i>
              <span class="ml-2">{{ train.label }}</span>
            </div>
          </ng-template>
          <ng-template let-train pTemplate="item">
            <div class="train-option">
              <i [class]="train.isComplexe ? 'pi pi-building' : 'pi pi-cog'"></i>
              <span class="ml-2">{{ train.label }}</span>
            </div>
          </ng-template>
        </p-dropdown>

        <p-button
          icon="pi pi-refresh"
          (onClick)="refresh()"
          [loading]="loading"
          severity="secondary"
          size="small">
        </p-button>
      </div>
    </div>

    <!-- Informations contextuelles -->
    <div class="context-info" *ngIf="evolutionData">
      <div class="info-item">
        <span class="info-label">Unité:</span>
        <span class="info-value">{{ currentUnite }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Année:</span>
        <span class="info-value">{{ currentAnnee }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Train:</span>
        <span class="info-value">{{ getSelectedTrainLabel() }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">Mode:</span>
        <span class="info-value">{{ selectedMode === 'sieges' ? 'Sièges de Causes' : 'Classes de Causes' }}</span>
      </div>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="statistics-panel" *ngIf="statistics && !loading">
    <div class="stat-card">
      <div class="stat-icon">
        <i class="pi pi-cog"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ statistics.nombreTrains }}</div>
        <div class="stat-label">Trains</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <i [class]="selectedMode === 'sieges' ? 'pi pi-sitemap' : 'pi pi-tags'"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ statistics.nombreCausesTotal }}</div>
        <div class="stat-label">{{ selectedMode === 'sieges' ? 'Sièges' : 'Classes' }} Total</div>
      </div>
    </div>

    <div class="stat-card total-card">
      <div class="stat-icon">
        <i class="pi pi-fire"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ formatNumber(statistics.totalGlobal) }}</div>
        <div class="stat-label">Total Gaz Torché</div>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon">
        <i class="pi pi-calendar"></i>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ statistics.moisLePlusActif.nomMois }}</div>
        <div class="stat-label">Mois le plus actif</div>
      </div>
    </div>
  </div>

  <!-- Messages d'état -->
  <p-message
    *ngIf="error"
    severity="error"
    [text]="error"
    >
  </p-message>

  <p-message
    *ngIf="!loading && (!evolutionData || evolutionData.trains.length === 0)"
    severity="info"
    text="Aucune donnée disponible pour la période sélectionnée">
  </p-message>

  <!-- Spinner de chargement -->
  <div class="loading-container" *ngIf="loading">
    <p-progressSpinner></p-progressSpinner>
    <p>Chargement des données d'évolution...</p>
  </div>

  <!-- Tableau d'évolution -->
  <div class="table-container" *ngIf="!loading && tableData.length > 0">
    <p-table
      [value]="tableData"
      [scrollable]="true"
      scrollHeight="600px"
      [tableStyle]="{'min-width': '1200px'}"
      styleClass="evolution-table">

      <ng-template pTemplate="header">
        <tr>
          <th *ngFor="let col of columns"
              [style.width]="col.width"
              [pSortableColumn]="col.sortable ? col.field : undefined">
            {{ col.header }}
            <p-sortIcon *ngIf="col.sortable" [field]="col.field"></p-sortIcon>
          </th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-row>
        <tr>
          <td *ngFor="let col of columns">
            <!-- Colonne cause -->
            <span *ngIf="col.field === 'cause'" class="cause-cell">
              <i [class]="selectedMode === 'sieges' ? 'pi pi-sitemap' : 'pi pi-tags'" class="cause-icon"></i>
              {{ row[col.field] }}
            </span>

            <!-- Colonnes numériques -->
            <span *ngIf="col.type === 'number'"
                  class="numeric-cell"
                  [ngClass]="{'has-value': isDefined(row[col.field])}">
              {{ formatNumber(row[col.field]) }}
            </span>

            <!-- Autres colonnes -->
            <span *ngIf="col.type !== 'number' && col.field !== 'cause'">
              {{ row[col.field] }}
            </span>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="columns.length" class="text-center">
            Aucune donnée disponible pour la sélection actuelle
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>

  <!-- Légende -->
  <div class="legend-container" *ngIf="!loading && tableData.length > 0">
    <div class="legend-item">
      <i class="pi pi-sitemap legend-icon"></i>
      <span class="legend-label">Sièges :</span>
      <span class="legend-description">Sièges de causes spécifiques</span>
    </div>
    <div class="legend-item">
      <i class="pi pi-tags legend-icon"></i>
      <span class="legend-label">Classes :</span>
      <span class="legend-description">Classes de causes regroupées</span>
    </div>
    <div class="legend-item">
      <i class="pi pi-building legend-icon"></i>
      <span class="legend-label">Complexe :</span>
      <span class="legend-description">Ensemble de l'unité (tous trains)</span>
    </div>
  </div>
</div>
