import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, effect } from '@angular/core';
import { EvolutionCausesTrainService } from '../../services/evolution-causes-train.service';
import {
  EvolutionCausesResponse,
  TrainCauseEvolutionData,
  EvolutionCausesTableRow,
  EvolutionCausesTableColumn,
  GlobalCauseStatistics,
  CauseDisplayMode,
  EvolutionCausesParams,
  TrainOption,
  CAUSE_MODE_OPTIONS,
  CauseModeOption,
  MOIS_COMPLETS
} from '../../../../model/evolution-causes-train.model';
import { CalendarService } from '../../../../services/calendar.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-evolution-causes',
  templateUrl: './evolution-causes.component.html',
  styleUrls: ['./evolution-causes.component.scss']
})
export class EvolutionCausesComponent implements OnInit, OnDestroy {

  // Configuration
  currentUnite: string = '5X3';
  currentAnnee: number = new Date().getFullYear();

  // Mode d'affichage et train sélectionné
  selectedMode: CauseDisplayMode = 'sieges';
  selectedTrain: string = 'COMPLEXE';

  // Options disponibles
  modeOptions: CauseModeOption[] = CAUSE_MODE_OPTIONS;
  trainsDisponibles: TrainOption[] = [];

  // Données principales
  evolutionData: EvolutionCausesResponse | null = null;

  // État du composant
  loading: boolean = false;
  error: string | null = null;

  // Données pour l'affichage
  tableData: EvolutionCausesTableRow[] = [];
  columns: EvolutionCausesTableColumn[] = [];
  statistics: GlobalCauseStatistics | null = null;

  // Subscriptions
  private subscriptions: Subscription = new Subscription();

  constructor(
    private evolutionService: EvolutionCausesTrainService,
    private calendarService: CalendarService
  ) {
    // Écouter les changements de calendrier
    effect(() => {
      const selectedDate = this.calendarService.selectedMonthYear();
      if (selectedDate) {
        this.currentAnnee = selectedDate.getFullYear();
        this.loadData();
      }
    });
  }

  ngOnInit(): void {
    this.initializeColumns();
    this.loadTrainsDisponibles();
    this.loadData();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Initialise les colonnes du tableau
   */
  private initializeColumns(): void {
    const firstColumnHeader = this.selectedMode === 'sieges' ? 'Sièges de Causes' : 'Classes de Causes';

    this.columns = [
      { field: 'cause', header: firstColumnHeader, width: '250px', sortable: true },
      { field: 'janvier', header: 'J', width: '80px', type: 'number' },
      { field: 'fevrier', header: 'F', width: '80px', type: 'number' },
      { field: 'mars', header: 'M', width: '80px', type: 'number' },
      { field: 'avril', header: 'A', width: '80px', type: 'number' },
      { field: 'mai', header: 'M', width: '80px', type: 'number' },
      { field: 'juin', header: 'J', width: '80px', type: 'number' },
      { field: 'juillet', header: 'J', width: '80px', type: 'number' },
      { field: 'aout', header: 'A', width: '80px', type: 'number' },
      { field: 'septembre', header: 'S', width: '80px', type: 'number' },
      { field: 'octobre', header: 'O', width: '80px', type: 'number' },
      { field: 'novembre', header: 'N', width: '80px', type: 'number' },
      { field: 'decembre', header: 'D', width: '80px', type: 'number' },
      { field: 'annee', header: 'Année', width: '100px', type: 'number', sortable: true }
    ];
  }

  /**
   * Charge la liste des trains disponibles
   */
  loadTrainsDisponibles(): void {
    const moisActuel = `0101${this.currentAnnee}`;

    const subscription = this.evolutionService.getTrainsDisponibles(this.currentUnite, moisActuel)
      .subscribe({
        next: (trains) => {
          this.trainsDisponibles = trains;
          // S'assurer que "COMPLEXE" est sélectionné par défaut
          if (!this.selectedTrain || !trains.find(t => t.value === this.selectedTrain)) {
            this.selectedTrain = 'COMPLEXE';
          }
        },
        error: (error) => {
          console.error('Erreur lors du chargement des trains:', error);
        }
      });

    this.subscriptions.add(subscription);
  }

  /**
   * Charge les données d'évolution
   */
  loadData(): void {
    this.loading = true;
    this.error = null;

    const params: EvolutionCausesParams = {
      unite: this.currentUnite,
      annee: this.currentAnnee,
      train: this.selectedTrain,
      mode: this.selectedMode
    };

    const subscription = this.evolutionService.getEvolutionCausesData(params)
      .subscribe({
        next: (data) => {
          this.evolutionData = data;
          this.updateTableData();
          this.calculateStatistics();
          this.loading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des données d\'évolution:', error);
          this.error = 'Erreur lors du chargement des données';
          this.loading = false;
        }
      });

    this.subscriptions.add(subscription);
  }

  /**
   * Met à jour les données du tableau
   */
  updateTableData(): void {
    if (!this.evolutionData) {
      this.tableData = [];
      return;
    }

    // Pour le mode "COMPLEXE", on agrège toutes les causes de tous les trains
    let allCauses: any[] = [];

    if (this.selectedTrain === 'COMPLEXE') {
      // Agréger les causes de tous les trains
      const causesMap = new Map<string, any>();

      this.evolutionData.trains.forEach(train => {
        train.causes.forEach(cause => {
          if (causesMap.has(cause.libelleCause)) {
            const existingCause = causesMap.get(cause.libelleCause);
            existingCause.totalAnnuel += cause.totalAnnuel;
            cause.quantitesMensuelles.forEach((monthly, index) => {
              existingCause.quantitesMensuelles[index].quantite += monthly.quantite;
            });
          } else {
            causesMap.set(cause.libelleCause, {
              ...cause,
              quantitesMensuelles: cause.quantitesMensuelles.map(m => ({ ...m }))
            });
          }
        });
      });

      allCauses = Array.from(causesMap.values());
    } else {
      // Données pour un train spécifique
      const train = this.evolutionData.trains.find(t => t.codeTrain === this.selectedTrain);
      allCauses = train ? train.causes : [];
    }

    this.tableData = allCauses.map(cause => {
      const row: EvolutionCausesTableRow = {
        cause: cause.libelleCause,
        mode: this.selectedMode,
        annee: cause.totalAnnuel
      };

      // Ajouter les quantités mensuelles
      cause.quantitesMensuelles.forEach((monthly: any) => {
        const moisKey = this.getMoisKey(monthly.mois);
        if (moisKey && monthly.quantite > 0) {
          (row as any)[moisKey] = monthly.quantite;
        }
      });

      return row;
    });
  }

  /**
   * Calcule les statistiques globales
   */
  calculateStatistics(): void {
    if (this.evolutionData) {
      this.statistics = this.evolutionService.calculateGlobalStatistics(this.evolutionData);
    }
  }

  /**
   * Gestionnaire de changement de mode (Sièges/Classes)
   */
  onModeChange(): void {
    this.initializeColumns();
    this.loadData();
  }

  /**
   * Gestionnaire de changement de train
   */
  onTrainChange(): void {
    this.loadData();
  }

  /**
   * Obtient la clé du mois pour le tableau
   */
  private getMoisKey(mois: number): string | null {
    const moisKeys: { [key: number]: string } = {
      1: 'janvier', 2: 'fevrier', 3: 'mars', 4: 'avril',
      5: 'mai', 6: 'juin', 7: 'juillet', 8: 'aout',
      9: 'septembre', 10: 'octobre', 11: 'novembre', 12: 'decembre'
    };
    return moisKeys[mois] || null;
  }

  /**
   * Formate un nombre pour l'affichage
   */
  formatNumber(value: number | undefined): string {
    if (value === undefined || value === 0) {
      return '';
    }
    return value.toLocaleString('fr-FR', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }

  /**
   * Vérifie si une valeur est définie et non nulle
   */
  isDefined(value: any): boolean {
    return value !== undefined && value !== null && value !== 0;
  }

  /**
   * Obtient le nom complet du mois
   */
  getMoisComplet(mois: number): string {
    return MOIS_COMPLETS[mois] || '';
  }

  /**
   * Obtient le libellé du train sélectionné
   */
  getSelectedTrainLabel(): string {
    const train = this.trainsDisponibles.find(t => t.value === this.selectedTrain);
    return train ? train.label : this.selectedTrain;
  }

  /**
   * Rafraîchit les données
   */
  refresh(): void {
    this.loadTrainsDisponibles();
    this.loadData();
  }
}
