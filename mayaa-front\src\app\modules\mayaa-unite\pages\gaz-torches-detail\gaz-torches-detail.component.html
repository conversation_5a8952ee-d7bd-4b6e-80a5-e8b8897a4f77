<div class="surface-ground px-1 py-3 md:px-2 lg:px-3">
  <div class="flex justify-content-between mb-3">
    <p-button icon="pi pi-arrow-left" label="Retour" (onClick)="onRetour()" styleClass="p-button-secondary"></p-button>
  </div>
  <div class="grid">
    <div class="col-8">
      <div class="surface-card shadow-2 border-round p-4">
        <p-chart type="doughnut" [data]="chartData" [options]="chartOptions"></p-chart>
      </div>
    </div>
    <div class="col-4">
      <div class="surface-card shadow-2 border-round p-4">
        <p-table [value]="donneesJournalieres" [columns]="cols" [scrollable]="true" scrollHeight="500px"
          styleClass="p-datatable-sm p-datatable-striped" [sortField]="'jour'" [sortOrder]="1">
          <ng-template pTemplate="header" let-columns>
            <tr>
              <th *ngFor="let col of columns" [pSortableColumn]="col.field">
                {{col.header}}
                <p-sortIcon [field]="col.field"></p-sortIcon>
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-rowData let-columns="columns">
            <tr [pTooltip]="rowData.analyse" tooltipPosition="left" [tooltipStyleClass]="'custom-tooltip'"
              *ngIf="rowData.analyse">
              <td *ngFor="let col of columns">
                <ng-container [ngSwitch]="col.field">
                  <ng-container *ngSwitchCase="'cm3GN'">
                    {{formatNumber(rowData[col.field])}}
                  </ng-container>
                  <ng-container *ngSwitchDefault>
                    {{rowData[col.field]}}
                  </ng-container>
                </ng-container>
              </td>
            </tr>
            <tr *ngIf="!rowData.analyse">
              <td *ngFor="let col of columns">
                <ng-container [ngSwitch]="col.field">
                  <ng-container *ngSwitchCase="'cm3GN'">
                    {{formatNumber(rowData[col.field])}}
                  </ng-container>
                  <ng-container *ngSwitchDefault>
                    {{rowData[col.field]}}
                  </ng-container>
                </ng-container>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<style>
::ng-deep .custom-tooltip {
  max-width: 250px;
  background-color: var(--surface-900);
  color: var(--surface-0);
}
</style>
