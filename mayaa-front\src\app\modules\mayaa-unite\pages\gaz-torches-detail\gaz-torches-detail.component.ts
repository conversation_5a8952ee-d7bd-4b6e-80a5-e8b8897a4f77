import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MayaaUniteService } from '../../services/mayaa-unite.service';
import { DetailMensuel, GazTorchesJournalier } from '../../../../model/ConsommationDetail';

@Component({
  selector: 'app-gaz-torches-detail',
  templateUrl: './gaz-torches-detail.component.html',
  styleUrls: ['./gaz-torches-detail.component.scss']
})
export class GazTorchesDetailComponent implements OnInit {
  chartData: any;
  chartOptions: any;
  donneesJournalieres: GazTorchesJournalier[] = [];
  cols: any[] = [];

  constructor(
    private mayaaUniteService: MayaaUniteService,
    private router: Router
  ) {}

  ngOnInit() {
    this.cols = [
      { field: 'jour', header: 'Jour' },
      { field: 'cause', header: 'Cause' },
      { field: 'cm3GN', header: 'Gaz Torchés (Cm³)' }
    ];

    this.mayaaUniteService.getGazTorchesDetail().subscribe(data => {
      this.prepareChartData(data);
      this.donneesJournalieres = data.donneesJournalieres as GazTorchesJournalier[];
    });
  }

  onRetour() {
    this.router.navigate(['unite/tdb']);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('fr-FR').format(value);
  }

  private prepareChartData(data: DetailMensuel) {
    const totalGazTorches = data.causes?.reduce((sum, cause) => sum + cause.cm3GN, 0) || 0;

    this.chartData = {
      labels: data.causes?.map(c => c.cause) || [],
      datasets: [{
        data: data.causes?.map(c => c.cm3GN) || [],
        backgroundColor: [
          '#FF9800',
          '#2196F3',
          '#4CAF50',
          '#9C27B0',
          '#E91E63'
        ],
        hoverBackgroundColor: [
          '#FFB74D',
          '#64B5F6',
          '#81C784',
          '#BA68C8',
          '#F06292'
        ]
      }]
    };

    this.chartOptions = {
      plugins: {
        title: {
          display: true,
          text: `Détail Gaz Torchés par Cause - ${data.mois} ${data.annee}`,
          font: { size: 16 }
        },
        tooltip: {
          callbacks: {
            label: function(context: any) {
              const value = context.raw;
              const percentage = (value / totalGazTorches * 100).toFixed(1);
              return `${context.label}: ${new Intl.NumberFormat('fr-FR').format(value)} Cm³ (${percentage}%)`;
            }
          }
        },
        legend: {
          position: 'right'
        },
        datalabels: {
          formatter: (value: number, ctx: any) => {
            const percentage = (value / totalGazTorches * 100).toFixed(1);
            return percentage + '%';
          },
          color: '#fff',
          font: {
            weight: 'bold'
          }
        }
      },
      responsive: true,
      cutout: '30%'
    };
  }
} 