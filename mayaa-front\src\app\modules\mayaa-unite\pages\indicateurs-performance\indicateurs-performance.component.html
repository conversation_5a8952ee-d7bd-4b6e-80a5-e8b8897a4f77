<div class="indicateurs-performance-container">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">
        <i class="pi pi-chart-line mr-2"></i>
        Indicateurs de Performance du complexe
      </h3>
      <button
        pButton
        type="button"
        icon="pi pi-refresh"
        class="p-button-outlined p-button-sm"
        (click)="onRefresh()"
        [loading]="loading">
      </button>
    </div>

    <div class="card-content">
      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <p-progressSpinner strokeWidth="3"></p-progressSpinner>
        <p class="loading-text">Chargement des indicateurs...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="error-container">
        <i class="pi pi-exclamation-triangle error-icon"></i>
        <p class="error-text">{{ error }}</p>
        <button pButton label="Réessayer" (click)="onRefresh()" class="p-button-sm"></button>
      </div>

      <!-- Content -->
      <div *ngIf="tableData && !loading && !error" class="content-container">

        <!-- Table -->
        <div class="table-container">
          <p-table
            [value]="[tableData.design, tableData.reelle]"
            styleClass="p-datatable-sm modern-table"
            [tableStyle]="{'min-width': '100%'}"
            responsiveLayout="scroll">

            <ng-template pTemplate="header">
              <tr>
                <th rowspan="2" class="row-header"></th>
                <th rowspan="2" class="text-center">
                  Nombre de<br>trains en<br>service
                </th>
                <th colspan="5" class="text-center performance-header">
                  Indicateurs de Performance (%)
                </th>
                <th colspan="5" class="text-center consumption-header">
                  Consommations
                </th>
              </tr>
              <tr>
                <th class="text-center">TP<br>(%)</th>
                <th class="text-center">TF<br>(%)</th>
                <th class="text-center">TC<br>(%)</th>
                <th class="text-center">TAP<br>(%)</th>
                <th class="text-center">TAI<br>(%)</th>
                <th class="text-center">Production<br>GNL<br>M³</th>
                <th class="text-center">Cons ED<br>L/m3 GNL</th>
                <th class="text-center">Taux<br>d'appoint<br>ED/Vap</th>
                <th class="text-center">Cons<br>vapeur<br>T/m3 GNL</th>
                <th class="text-center">Cons Elect<br>MWH/m3 GNL</th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-row let-rowIndex="rowIndex">
              <tr [ngClass]="{'design-row': row.type === 'design', 'reelle-row': row.type === 'reelle'}">
                <td class="row-label">
                  <span class="row-type-badge" [ngClass]="row.type">
                    {{ row.type === 'design' ? 'Design' : 'Réelle' }}
                  </span>
                </td>
                <td class="text-center number-cell">{{ formatNumber(row.nombreTrains, 0) }}</td>
                <td class="text-center number-cell">{{ formatPercentage(row.tp) }}</td>
                <td class="text-center number-cell">{{ formatPercentage(row.tf) }}</td>
                <td class="text-center number-cell">{{ formatPercentage(row.tc) }}</td>
                <td class="text-center number-cell">{{ formatPercentage(row.tap) }}</td>
                <td class="text-center number-cell">{{ formatPercentage(row.tai) }}</td>
                <td class="text-center number-cell">{{ formatNumber(row.productionGNL, 0) }}</td>
                <td class="text-center number-cell">{{ formatNumber(row.consED) }}</td>
                <td class="text-center number-cell">{{ formatNumber(row.tauxAppoint) }}</td>
                <td class="text-center number-cell">{{ formatNumber(row.consVapeur) }}</td>
                <td class="text-center number-cell">{{ formatNumber(row.consElect) }}</td>
              </tr>
            </ng-template>
          </p-table>
        </div>

        <!-- Bilan GN-GNL Chart -->

      </div>
    </div>
  </div>
  <div *ngIf="bilanData" class="bilan-chart-container">
    <div class="chart-header">
      <h4 class="chart-title">
        <i class="pi pi-chart-pie mr-2"></i>
        Bilan GN-GNL
      </h4>

    </div>

    <!-- Résumé GN Reçu -->
    <div class="gn-summary">
      <div class="summary-card">
        <div class="summary-icon">
          <i class="pi pi-database"></i>
        </div>
        <div class="summary-info">
          <h5>GN Reçu Total</h5>
          <p class="summary-value">{{ formatNumber(bilanData.gnRecu || 0, 0) }} CM³</p>
        </div>
      </div>
    </div>

    <!-- Graphiques côte à côte -->
    <div class="charts-section">
      <div class="charts-subtitle">
        <p>Répartition du GN Reçu et décomposition de l'Utilisation Interne</p>
      </div>

      <div class="charts-grid">
        <!-- Graphique Principal -->
        <div class="chart-item">
          <h5 class="chart-item-title">GN Reçu Total</h5>
          <div class="chart-wrapper">
            <p-chart
              type="pie"
              [data]="mainChartData"
              [options]="mainChartOptions"
              width="300"
              height="300">
            </p-chart>
          </div>
        </div>

        <!-- Sous-graphique (Utilisation Interne) -->
        <div class="chart-item">
          <h5 class="chart-item-title">Utilisation Interne</h5>
          <div class="chart-wrapper">
            <p-chart
              type="pie"
              [data]="subChartData"
              [options]="subChartOptions"
              width="300"
              height="300">
            </p-chart>
          </div>
        </div>
      </div>

      <!-- Tableau de données avec sous-lignes -->
      <div class="chart-data-table">
        <p-table [value]="tableRowsWithSubRows" styleClass="p-datatable-sm">
          <ng-template pTemplate="header">
            <tr>
              <th>Composant</th>
              <th class="text-right">Valeur (CM³)</th>
              <th class="text-right">% du GN Total</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-row>
            <tr [ngClass]="{'main-row': row.isMain, 'sub-row': row.isSub}"
                [style.border-left]="'4px solid ' + row.color">
              <td [ngClass]="{'sub-item': row.isSub}">
                <span class="color-indicator" [style.background-color]="row.color"></span>
                <span *ngIf="row.isSub" class="sub-indicator">└─</span>
                {{ row.label }}
              </td>
              <td class="text-right number-cell">{{ formatNumber(row.value, 0) }}</td>
              <td class="text-right number-cell">{{ formatNumber(row.percent, 2) }}%</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>


  </div>
  <!-- Tableau des Chaudières -->
  <div *ngIf="chaudieresData" class="chaudieres-table-container">
    <div class="table-header">
      <h4 class="table-title">
        <i class="pi pi-cog mr-2"></i>
        Utilisation des chaudières
      </h4>
    </div>

    <div class="chaudieres-table">
      <p-table [value]="chaudieresData.rows" styleClass="p-datatable-sm chaudieres-table-style">
        <ng-template pTemplate="header">
          <tr>
            <th rowspan="2" class="text-center chaudieres-header">Chaudières</th>
            <th rowspan="2" class="text-center">
              Capacité<br>Installée<br>T/H
            </th>
            <th rowspan="2" class="text-center">
              Nombre de<br>chaudières<br>utilisées
            </th>
            <th colspan="2" class="text-center arret-header">
              Nombre d'arrêt
            </th>
            <th rowspan="2" class="text-center">
              Production<br>vapeur
            </th>
            <th rowspan="2" class="text-center">
              Consommation Fuel gaz<br>(eq GN) CM3
            </th>
          </tr>
          <tr>
            <th class="text-center">Volontaire</th>
            <th class="text-center">Déclenchement</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-row let-rowIndex="rowIndex">
          <tr [ngClass]="{'chaudieres-row': true}">
            <td class="text-center chaudieres-name">
              <span class="chaudieres-badge" [ngClass]="row.typeChaudieres.toLowerCase()">
                {{ row.typeChaudieres }}
              </span>
            </td>
            <td class="text-center number-cell">{{ formatNumber(row.capaciteInstallee, 0) }}</td>
            <td class="text-center number-cell">{{ formatNumber(row.chaudieresUtilises, 0) }}</td>
            <td class="text-center number-cell">{{ formatNumber(row.nombreArretVolontaire, 0) }}</td>
            <td class="text-center number-cell">{{ formatNumber(row.nombreArretDeclenchement, 0) }}</td>
            <td class="text-center number-cell">{{ formatNumber(row.productionVapeur, 3) }}</td>
            <td class="text-center number-cell">{{ formatNumber(row.consommationFuelGaz, 0) }}</td>
          </tr>
        </ng-template>

        <!-- Ligne de totaux -->
        <ng-template pTemplate="footer">
          <tr class="totals-row">
            <td class="text-center font-bold">TOTAL</td>
            <td class="text-center number-cell font-bold">{{ formatNumber(chaudieresData.totalCapaciteInstallee, 0) }}
            </td>
            <td class="text-center number-cell font-bold">{{ formatNumber(chaudieresData.totalChaudieresUtilises, 0) }}
            </td>
            <td class="text-center number-cell">-</td>
            <td class="text-center number-cell">-</td>
            <td class="text-center number-cell font-bold">{{ formatNumber(chaudieresData.totalProductionVapeur, 3) }}</td>
            <td class="text-center number-cell font-bold">{{ formatNumber(chaudieresData.totalConsommationFuelGaz, 0) }}
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
