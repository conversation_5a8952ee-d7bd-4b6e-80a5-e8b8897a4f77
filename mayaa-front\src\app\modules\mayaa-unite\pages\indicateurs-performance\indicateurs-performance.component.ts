import { Component, OnInit, OnDestroy, effect, Input } from '@angular/core';
import { MayaaUniteService } from '../../services/mayaa-unite.service';
import { IndicateurPerformanceUnite, IndicateurPerformanceTableData, IndicateurPerformanceRow } from '../../../../model/IndicateurPerformanceUnite';
import { BilanGnGnlData, ChartDataItem } from '../../../../model/BilanGnGnl';
import { AutoConsMens } from '../../../../model/AutoConsMens';
import { GazTorcheeParCauseTorchage } from '../../../../model/GazTorcheeParCauseTorchage';
import { UtilisationChaudieres, ChaudieresTableData, ChaudieresTableRow } from '../../../../model/UtilisationChaudieres';
import { forkJoin } from 'rxjs';
import { CalendarService } from '../../../../services/calendar.service';

@Component({
  selector: 'app-indicateurs-performance',
  templateUrl: './indicateurs-performance.component.html',
  styleUrls: ['./indicateurs-performance.component.scss']
})
export class IndicateursPerformanceComponent implements OnInit, OnDestroy {
  tableData: IndicateurPerformanceTableData | null = null;
  loading: boolean = false;
  error: string | null = null;

  // Propriétés pour le graphique Bilan GN-GNL
  bilanData: BilanGnGnlData | null = null;
  mainChartData: any;
  mainChartOptions: any;
  subChartData: any;
  subChartOptions: any;
  mainChartItems: ChartDataItem[] = [];
  subChartItems: ChartDataItem[] = [];
  tableRowsWithSubRows: any[] = [];

  // Propriétés pour le tableau des chaudières
  chaudieresData: ChaudieresTableData | null = null;

  // Couleurs pour les graphiques
  private colors = {
    gnTransforme: '#3b82f6',      // Bleu
    utilisationInterne: '#f59e0b', // Orange
    gazTorches: '#fb923c',        // Orange plus clair
    autoconsommationNette: '#f97316' // Orange foncé
  };

  // Données design statiques (à adapter selon vos besoins)
  private designData: IndicateurPerformanceRow = {
    type: 'design',
    nombreTrains: 6,
    tp: 100,
    tf: 100,
    tc: 100,
    tap: 0, // Pas de valeur design pour TAP
    tai: 0, // Pas de valeur design pour TAI
    productionGNL: 1674000,
    consED: 120,
    tauxAppoint: 10,
    consVapeur: 0.92,
    consElect: 0.02
  };

  constructor(
    private mayaaUniteService: MayaaUniteService,
    private calendarService: CalendarService
  ) {
    // Effect pour écouter les changements de date/unité et recharger les données
    effect(() => {
      const unite = this.calendarService.selectedUnite();
      const pmois = this.calendarService.pmoisFormat();

      // Recharger les données quand la date ou l'unité change
      this.loadIndicateurs(unite, pmois);
    });
  }

  ngOnInit(): void {
    this.initChartOptions();
    // Les données seront chargées automatiquement via l'effect dans le constructeur
  }

  ngOnDestroy(): void {
    // Cleanup automatique des effects Angular
  }

  loadIndicateurs(unite: string, pmois: string): void {
    this.loading = true;
    this.error = null;

    // Appels parallèles pour récupérer toutes les données
    forkJoin({
      indicateurs: this.mayaaUniteService.getIndicateurPerformance(pmois, unite),
      autoCons: this.mayaaUniteService.getBilanUnite(
        pmois.substring(2, 4), // mois
        pmois.substring(4, 8), // année
        unite
      ),
      gazTorches: this.mayaaUniteService.getGazTorcheeParCauseTorchage(unite, pmois),
      chaudieres: this.mayaaUniteService.getUtilisationChaudieres(unite, pmois)
    }).subscribe({
      next: (data) => {
        // Traitement des indicateurs de performance
        if (data.indicateurs && data.indicateurs.length > 0) {
          const indicateur = data.indicateurs[0];
          this.tableData = {
            design: this.designData,
            reelle: this.mapToTableRow(indicateur)
          };
        }

        // Traitement des données du bilan GN-GNL
        this.processBilanData(data.autoCons, data.gazTorches);

        // Traitement des données des chaudières
        this.processChaudieresData(data.chaudieres);

        this.loading = false;
      },
      error: (err) => {
        this.error = 'Erreur lors du chargement des données';
        console.error('Erreur:', err);
        this.loading = false;
      }
    });
  }

  private mapToTableRow(indicateur: IndicateurPerformanceUnite): IndicateurPerformanceRow {
    return {
      type: 'reelle',
      nombreTrains: indicateur.trainMarche || 0,
      tp: indicateur.tp || 0,
      tf: indicateur.tf || 0,
      tc: indicateur.tc || 0,
      tap: indicateur.tap || 0,
      tai: indicateur.tai || 0,
      productionGNL: indicateur.production || 0,
      consED: 0, // À calculer ou récupérer d'une autre source
      tauxAppoint: 0, // À calculer ou récupérer d'une autre source
      consVapeur: 0, // À calculer ou récupérer d'une autre source
      consElect: 0 // À calculer ou récupérer d'une autre source
    };
  }

  formatNumber(value: number, decimals: number = 2): string {
    if (value === null || value === undefined) return '/';
    return value.toLocaleString('fr-FR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  formatPercentage(value: number): string {
    if (value === null || value === undefined || value === 0) return '/';
    return this.formatNumber(value, 2);
  }

  onRefresh(): void {
    const unite = this.calendarService.selectedUnite();
    const pmois = this.calendarService.pmoisFormat();
    this.loadIndicateurs(unite, pmois);
  }

  // Méthodes pour le graphique Bilan GN-GNL
  private processBilanData(autoCons: AutoConsMens, gazTorches: GazTorcheeParCauseTorchage[]): void {
    // Calcul des totaux
    const gnRecu = autoCons.receptionGnMois || 0;
    const totalGazTorches = gazTorches.reduce((sum, item) =>
      sum + (item.quantiteGazTorchee || 0), 0);
    const autoconsommationNette = autoCons.autoConsMoisNet || 0;
    const utilisationInterne = totalGazTorches + autoconsommationNette;
    const gnTransforme = gnRecu - utilisationInterne;

    this.bilanData = {
      gnRecu,
      gnTransforme,
      utilisationInterne,
      gazTorches: totalGazTorches,
      autoconsommationNette,
      gnTransformePercent: gnRecu > 0 ? (gnTransforme / gnRecu) * 100 : 0,
      utilisationInternePercent: gnRecu > 0 ? (utilisationInterne / gnRecu) * 100 : 0,
      gazTorchesPercent: utilisationInterne > 0 ? (totalGazTorches / utilisationInterne) * 100 : 0,
      autoconsommationNettePercent: utilisationInterne > 0 ? (autoconsommationNette / utilisationInterne) * 100 : 0
    };

    this.updateChartData();
  }

  private updateChartData(): void {
    if (!this.bilanData) return;

    // Graphique principal : GN Reçu décomposé
    this.mainChartItems = [
      {
        label: 'GN Transformé',
        value: this.bilanData.gnTransforme,
        percent: this.bilanData.gnTransformePercent,
        color: this.colors.gnTransforme,
        unit: 'CM³'
      },
      {
        label: 'Utilisation Interne',
        value: this.bilanData.utilisationInterne,
        percent: this.bilanData.utilisationInternePercent,
        color: this.colors.utilisationInterne,
        unit: 'CM³'
      }
    ];

    // Sous-graphique : Utilisation Interne décomposée
    this.subChartItems = [
      {
        label: 'Gaz Torchés',
        value: this.bilanData.gazTorches,
        percent: this.bilanData.gazTorchesPercent,
        color: this.colors.gazTorches,
        unit: 'CM³'
      },
      {
        label: 'Autoconsommation Nette',
        value: this.bilanData.autoconsommationNette,
        percent: this.bilanData.autoconsommationNettePercent,
        color: this.colors.autoconsommationNette,
        unit: 'CM³'
      }
    ];

    this.updatePrimeNGChartData();
    this.createTableRowsWithSubRows();
  }

  private updatePrimeNGChartData(): void {
    // Configuration du graphique principal
    this.mainChartData = {
      labels: this.mainChartItems.map(item => item.label),
      datasets: [{
        data: this.mainChartItems.map(item => item.value),
        backgroundColor: this.mainChartItems.map(item => item.color),
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    };

    // Configuration du sous-graphique
    this.subChartData = {
      labels: this.subChartItems.map(item => item.label),
      datasets: [{
        data: this.subChartItems.map(item => item.value),
        backgroundColor: this.subChartItems.map(item => item.color),
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    };
  }

  private initChartOptions(): void {
    this.mainChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const item = this.mainChartItems[context.dataIndex];
              if (item) {
                return `${item.label}: ${this.formatNumber(item.value, 0)} ${item.unit} (${this.formatNumber(item.percent, 2)}%)`;
              }
              return '';
            }
          }
        }
      }
    };

    this.subChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const item = this.subChartItems[context.dataIndex];
              if (item) {
                return `${item.label}: ${this.formatNumber(item.value, 0)} ${item.unit} (${this.formatNumber(item.percent, 2)}%)`;
              }
              return '';
            }
          }
        }
      }
    };
  }

  private createTableRowsWithSubRows(): void {
    if (!this.bilanData) return;

    this.tableRowsWithSubRows = [
      // GN Transformé (ligne principale)
      {
        label: 'GN Transformé',
        value: this.bilanData.gnTransforme,
        percent: this.bilanData.gnTransformePercent,
        color: this.colors.gnTransforme,
        isMain: true,
        isSub: false
      },
      // Utilisation Interne (ligne principale)
      {
        label: 'Utilisation Interne',
        value: this.bilanData.utilisationInterne,
        percent: this.bilanData.utilisationInternePercent,
        color: this.colors.utilisationInterne,
        isMain: true,
        isSub: false
      },
      // Gaz Torchés (sous-ligne)
      {
        label: 'Gaz Torchés',
        value: this.bilanData.gazTorches,
        percent: (this.bilanData.gazTorches / (this.bilanData.gnRecu || 1)) * 100,
        color: this.colors.gazTorches,
        isMain: false,
        isSub: true
      },
      // Autoconsommation Nette (sous-ligne)
      {
        label: 'Autoconsommation Nette',
        value: this.bilanData.autoconsommationNette,
        percent: (this.bilanData.autoconsommationNette / (this.bilanData.gnRecu || 1)) * 100,
        color: this.colors.autoconsommationNette,
        isMain: false,
        isSub: true
      }
    ];
  }

  // Méthodes pour le tableau des chaudières
  private processChaudieresData(chaudieres: UtilisationChaudieres[]): void {
    if (!chaudieres || chaudieres.length === 0) {
      this.chaudieresData = null;
      return;
    }

    // Transformation des données pour le tableau
    const rows: ChaudieresTableRow[] = chaudieres.map(item => ({
      typeChaudieres: this.formatTypeChaudieres(item.typeChaudieres),
      capaciteInstallee: item.capaciteInstallee,
      chaudieresUtilises: item.chaudieresUtiliseDesign,
      nombreArretVolontaire: item.nombreArretVolontaire,
      nombreArretDeclenchement: item.nombreArretDeclenchement,
      productionVapeur: item.productionVapeur,
      consommationFuelGaz: item.consommationFuelGaz
    }));

    // Calcul des totaux
    const totalCapaciteInstallee = rows.reduce((sum, row) => sum + row.capaciteInstallee, 0);
    const totalChaudieresUtilises = rows.reduce((sum, row) => sum + row.chaudieresUtilises, 0);
    const totalProductionVapeur = rows.reduce((sum, row) => sum + row.productionVapeur, 0);
    const totalConsommationFuelGaz = rows.reduce((sum, row) => sum + row.consommationFuelGaz, 0);

    this.chaudieresData = {
      rows,
      totalCapaciteInstallee,
      totalChaudieresUtilises,
      totalProductionVapeur,
      totalConsommationFuelGaz
    };
  }

  private formatTypeChaudieres(type: string): string {
    // Simplifier le nom du type de chaudières pour l'affichage
    if (type.includes('ABB')) {
      return 'ABB';
    } else if (type.includes('IHI')) {
      return 'IHI';
    }
    return type;
  }
}
