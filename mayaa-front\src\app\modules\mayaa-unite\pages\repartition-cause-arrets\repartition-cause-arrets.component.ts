import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ArretsService } from '../../services/arrets.service';
import { CalendarService } from '../../../../services/calendar.service';
import { RepartitionArretsParCause } from '../../../../model/arrets.interface';

@Component({
  selector: 'app-repartition-cause-arrets',
  templateUrl: './repartition-cause-arrets.component.html',
  styleUrls: ['./repartition-cause-arrets.component.scss']
})
export class RepartitionCauseArretsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Données
  repartitionData: RepartitionArretsParCause[] = [];
  repartitionInternes: RepartitionArretsParCause[] = [];
  repartitionExternes: RepartitionArretsParCause[] = [];
  repartitionClasses: RepartitionArretsParCause[] = [];
  loading = false;

  // Configuration du tableau
  cols = [
    { field: 'cause', header: 'Cause', width: '300px' },
    { field: 'typeCause', header: 'Type', width: '120px' },
    { field: 'quantiteArrets', header: 'Quantité', width: '100px' },
    { field: 'pourcentage', header: 'Pourcentage (%)', width: '150px' }
  ];

  // Données pour les graphiques en secteurs
  pieChartDataInternes: any;
  pieChartDataExternes: any;
  pieChartDataClasses: any;
  pieChartOptions: any;

  constructor(
    private arretsService: ArretsService,
    private calendarService: CalendarService
  ) {
    this.initPieChartOptions();
  }

  ngOnInit(): void {
    // Écouter les changements de calendrier et d'unité
    combineLatest([
      this.calendarService.selectedDate$,
      this.calendarService.selectedUnite$
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([date, unite]) => {
      if (date && unite) {
        this.chargerDonnees(unite, this.formatDateForApi(date));
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private chargerDonnees(unite: string, mois: string): void {
    this.loading = true;

    // Charger toutes les données de répartition
    this.arretsService.getRepartitionParCause(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.repartitionData = data;
      this.repartitionInternes = data.filter(item => item.typeCause === 'INTERNE');
      this.repartitionExternes = data.filter(item => item.typeCause === 'EXTERNE');
      this.repartitionClasses = data.filter(item => item.typeCause === 'CLASSE');
      this.preparerDonneesPieCharts(unite, mois);
      this.loading = false;
    });
  }

  private preparerDonneesPieCharts(unite: string, mois: string): void {
    // Charger les données pour les graphiques en secteurs
    this.arretsService.getDonneesPieChartCause(unite, mois, 'INTERNE').pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.pieChartDataInternes = this.creerDonneesPieChart(data, 'Causes Internes');
    });

    this.arretsService.getDonneesPieChartCause(unite, mois, 'EXTERNE').pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.pieChartDataExternes = this.creerDonneesPieChart(data, 'Causes Externes');
    });

    this.arretsService.getDonneesPieChartCause(unite, mois, 'CLASSE').pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.pieChartDataClasses = this.creerDonneesPieChart(data, 'Classes de Causes');
    });
  }

  private creerDonneesPieChart(data: RepartitionArretsParCause[], titre: string): any {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
      '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];

    return {
      labels: data.map(item => item.cause),
      datasets: [{
        label: titre,
        data: data.map(item => item.pourcentage),
        backgroundColor: colors.slice(0, data.length),
        borderColor: colors.slice(0, data.length),
        borderWidth: 1
      }]
    };
  }

  private initPieChartOptions(): void {
    this.pieChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              return `${context.label}: ${context.parsed}%`;
            }
          }
        }
      }
    };
  }

  private formatDateForApi(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  // Méthodes utilitaires pour l'affichage
  getTypeBadgeClass(type: string): string {
    switch(type) {
      case 'INTERNE': return 'badge-interne';
      case 'EXTERNE': return 'badge-externe';
      case 'CLASSE': return 'badge-classe';
      default: return 'badge-default';
    }
  }

  formatNumber(value: number): string {
    return value?.toFixed(1) || '0.0';
  }

  getTotalQuantite(data: RepartitionArretsParCause[]): number {
    return data.reduce((total, item) => total + item.quantiteArrets, 0);
  }

  getTotalPourcentage(data: RepartitionArretsParCause[]): number {
    return data.reduce((total, item) => total + item.pourcentage, 0);
  }
}
