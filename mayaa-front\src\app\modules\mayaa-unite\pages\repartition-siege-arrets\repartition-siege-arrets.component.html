<div class="grid">
  <!-- Graphiques en secteurs côte à côte -->
  <div class="col-12 lg:col-6">
    <p-card header="Répartition par Sièges - Causes Internes">
      <div class="chart-container">
        <p-chart
          type="pie"
          [data]="pieChartDataInternes"
          [options]="pieChartOptions"
          *ngIf="pieChartDataInternes">
        </p-chart>
      </div>
      <div class="mt-3 text-center">
        <p class="font-semibold">
          Total: {{ getTotalQuantite(repartitionInternes) }} arrêts
          ({{ formatNumber(getTotalPourcentage(repartitionInternes)) }}%)
        </p>
      </div>
    </p-card>
  </div>

  <div class="col-12 lg:col-6">
    <p-card header="Répartition par Sièges - Causes Externes">
      <div class="chart-container">
        <p-chart
          type="pie"
          [data]="pieChartDataExternes"
          [options]="pieChartOptions"
          *ngIf="pieChartDataExternes">
        </p-chart>
      </div>
      <div class="mt-3 text-center">
        <p class="font-semibold">
          Total: {{ getTotalQuantite(repartitionExternes) }} arrêts
          ({{ formatNumber(getTotalPourcentage(repartitionExternes)) }}%)
        </p>
      </div>
    </p-card>
  </div>

  <!-- Tableau détaillé -->
  <div class="col-12">
    <p-card header="Détail de la répartition par siège">
      <p-tabView>
        <!-- Onglet Toutes les données -->
        <p-tabPanel header="Toutes les données">
          <p-table
            [value]="repartitionData"
            [loading]="loading"
            [scrollable]="true"
            scrollHeight="400px"
            [rowHover]="true"
            styleClass="p-datatable-sm">

            <ng-template pTemplate="header">
              <tr>
                <th *ngFor="let col of cols" [style.width]="col.width">
                  {{ col.header }}
                </th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData>
              <tr>
                <td>{{ rowData.siegeCause }}</td>
                <td>
                  <span [ngClass]="getTypeBadgeClass(rowData.typeSiege)" class="badge">
                    {{ rowData.typeSiege }}
                  </span>
                </td>
                <td class="text-right">{{ formatNumber(rowData.quantiteArrets) }}</td>
                <td class="text-right">{{ formatNumber(rowData.pourcentage) }}%</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td [attr.colspan]="cols.length" class="text-center">
                  Aucune donnée disponible
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-tabPanel>

        <!-- Onglet Causes Internes -->
        <p-tabPanel header="Causes Internes">
          <p-table
            [value]="repartitionInternes"
            [loading]="loading"
            [scrollable]="true"
            scrollHeight="400px"
            [rowHover]="true"
            styleClass="p-datatable-sm">

            <ng-template pTemplate="header">
              <tr>
                <th>Siège de la cause</th>
                <th>Quantité</th>
                <th>Pourcentage (%)</th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData>
              <tr>
                <td>{{ rowData.siegeCause }}</td>
                <td class="text-right">{{ formatNumber(rowData.quantiteArrets) }}</td>
                <td class="text-right">{{ formatNumber(rowData.pourcentage) }}%</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="3" class="text-center">
                  Aucune donnée disponible
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-tabPanel>

        <!-- Onglet Causes Externes -->
        <p-tabPanel header="Causes Externes">
          <p-table
            [value]="repartitionExternes"
            [loading]="loading"
            [scrollable]="true"
            scrollHeight="400px"
            [rowHover]="true"
            styleClass="p-datatable-sm">

            <ng-template pTemplate="header">
              <tr>
                <th>Siège de la cause</th>
                <th>Quantité</th>
                <th>Pourcentage (%)</th>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-rowData>
              <tr>
                <td>{{ rowData.siegeCause }}</td>
                <td class="text-right">{{ formatNumber(rowData.quantiteArrets) }}</td>
                <td class="text-right">{{ formatNumber(rowData.pourcentage) }}%</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="emptymessage">
              <tr>
                <td colspan="3" class="text-center">
                  Aucune donnée disponible
                </td>
              </tr>
            </ng-template>
          </p-table>
        </p-tabPanel>
      </p-tabView>
    </p-card>
  </div>
</div>
