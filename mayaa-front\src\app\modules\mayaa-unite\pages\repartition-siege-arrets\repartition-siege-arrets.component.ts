import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Subject, takeUntil, combineLatest } from 'rxjs';
import { ArretsService } from '../../services/arrets.service';
import { CalendarService } from '../../../../services/calendar.service';
import { RepartitionArretsParSiege } from '../../../../model/arrets.interface';

@Component({
  selector: 'app-repartition-siege-arrets',
  templateUrl: './repartition-siege-arrets.component.html',
  styleUrls: ['./repartition-siege-arrets.component.scss']
})
export class RepartitionSiegeArretsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Données
  repartitionData: RepartitionArretsParSiege[] = [];
  repartitionInternes: RepartitionArretsParSiege[] = [];
  repartitionExternes: RepartitionArretsParSiege[] = [];
  loading = false;

  // Configuration du tableau
  cols = [
    { field: 'siegeCause', header: 'Siège de la cause', width: '300px' },
    { field: 'typeSiege', header: 'Type', width: '120px' },
    { field: 'quantiteArrets', header: 'Quantité', width: '100px' },
    { field: 'pourcentage', header: 'Pourcentage (%)', width: '150px' }
  ];

  // Données pour les graphiques en secteurs
  pieChartDataInternes: any;
  pieChartDataExternes: any;
  pieChartOptions: any;

  constructor(
    private arretsService: ArretsService,
    private calendarService: CalendarService
  ) {
    this.initPieChartOptions();
  }

  ngOnInit(): void {
    // Écouter les changements de calendrier et d'unité
    combineLatest([
      this.calendarService.selectedDate$,
      this.calendarService.selectedUnite$
    ]).pipe(
      takeUntil(this.destroy$)
    ).subscribe(([date, unite]) => {
      if (date && unite) {
        this.chargerDonnees(unite, this.formatDateForApi(date));
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private chargerDonnees(unite: string, mois: string): void {
    this.loading = true;

    // Charger toutes les données de répartition
    this.arretsService.getRepartitionParSiege(unite, mois).pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.repartitionData = data;
      this.repartitionInternes = data.filter(item => item.typeSiege === 'INTERNE');
      this.repartitionExternes = data.filter(item => item.typeSiege === 'EXTERNE');
      this.preparerDonneesPieCharts(unite, mois);
      this.loading = false;
    });
  }

  private preparerDonneesPieCharts(unite: string, mois: string): void {
    // Charger les données pour les graphiques en secteurs
    this.arretsService.getDonneesPieChartSiege(unite, mois, 'INTERNE').pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.pieChartDataInternes = this.creerDonneesPieChart(data, 'Causes Internes');
    });

    this.arretsService.getDonneesPieChartSiege(unite, mois, 'EXTERNE').pipe(
      takeUntil(this.destroy$)
    ).subscribe(data => {
      this.pieChartDataExternes = this.creerDonneesPieChart(data, 'Causes Externes');
    });
  }

  private creerDonneesPieChart(data: RepartitionArretsParSiege[], titre: string): any {
    const colors = [
      '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
      '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
    ];

    return {
      labels: data.map(item => item.siegeCause),
      datasets: [{
        label: titre,
        data: data.map(item => item.pourcentage),
        backgroundColor: colors.slice(0, data.length),
        borderColor: colors.slice(0, data.length),
        borderWidth: 1
      }]
    };
  }

  private initPieChartOptions(): void {
    this.pieChartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              return `${context.label}: ${context.parsed}%`;
            }
          }
        }
      }
    };
  }

  private formatDateForApi(date: Date): string {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString();
    return `${day}${month}${year}`;
  }

  // Méthodes utilitaires pour l'affichage
  getTypeBadgeClass(type: string): string {
    return type === 'INTERNE' ? 'badge-interne' : 'badge-externe';
  }

  formatNumber(value: number): string {
    return value?.toFixed(1) || '0.0';
  }

  getTotalQuantite(data: RepartitionArretsParSiege[]): number {
    return data.reduce((total, item) => total + item.quantiteArrets, 0);
  }

  getTotalPourcentage(data: RepartitionArretsParSiege[]): number {
    return data.reduce((total, item) => total + item.pourcentage, 0);
  }
}
