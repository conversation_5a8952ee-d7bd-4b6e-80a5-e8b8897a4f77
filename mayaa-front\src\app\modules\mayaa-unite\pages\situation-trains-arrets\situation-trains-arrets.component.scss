// Styles pour le composant situation des trains arrêts

.stat-card {
  padding: 1.5rem;
  border-radius: 0.5rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
  }

  .stat-percentage {
    font-size: 0.75rem;
    font-weight: 600;
  }

  &.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  &.internes {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #065f46;
  }

  &.externes {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #92400e;
  }

  &.complexe {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #7c2d12;
  }
}

.situation-content {
  .section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
  }

  .analysis-text {
    line-height: 1.6;
    color: #4b5563;
    margin-bottom: 0;
  }

  .stats-row {
    display: flex;
    gap: 1rem;

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1f2937;
      }

      .stat-desc {
        font-size: 0.75rem;
        color: #6b7280;
        text-transform: uppercase;
      }
    }
  }
}

.causes-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;

  .cause-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #e5e7eb;
    color: #374151;

    &.siege-tag {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.recurrent-tag {
      background-color: #fef3c7;
      color: #92400e;
    }
  }
}

.analysis-section {
  .indicators-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;

    .indicator-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem;
      background-color: #f9fafb;
      border-radius: 0.375rem;

      .indicator-label {
        font-size: 0.875rem;
        color: #6b7280;
      }

      .indicator-value {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
      }
    }
  }
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.badge-interne {
  background-color: #dcfce7;
  color: #166534;
}

.badge-externe {
  background-color: #fef3c7;
  color: #92400e;
}

.text-center {
  text-align: center;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}
