<div class="grid">
  <!-- Sélecteur de train -->
  <div class="col-12">
    <p-card>
      <div class="flex align-items-center gap-3 mb-3">
        <label for="trainSelect" class="font-semibold">Train :</label>
        <p-dropdown
          id="trainSelect"
          [options]="trainsDisponibles"
          [(ngModel)]="trainSelectionne"
          (onChange)="onTrainChange()"
          placeholder="Sélectionner un train"
          [style]="{'min-width': '200px'}">
        </p-dropdown>
      </div>
    </p-card>
  </div>

  <!-- Tableau de synthèse -->
  <div class="col-12">
    <p-card header="Synthèse des arrêts - MAP, AC nette, GT par trains">
      <p-table
        [value]="syntheseData"
        [loading]="loading"
        [scrollable]="true"
        scrollHeight="400px"
        [rowHover]="true"
        styleClass="p-datatable-sm">

        <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of cols" [style.width]="col.width">
              {{ col.header }}
            </th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rowData>
          <tr [ngClass]="getRowClass(rowData)">
            <td>{{ rowData.codeTrain }}</td>
            <td class="text-right">{{ formatNumber(rowData.nbreHeures) }}</td>
            <td class="text-right">{{ formatNumber(rowData.map) }}%</td>
            <td class="text-right">{{ formatNumber(rowData.acNette) }}%</td>
            <td class="text-right">{{ formatNumber(rowData.gtInterne) }}</td>
            <td class="text-right">{{ formatNumber(rowData.gtExterne) }}</td>
            <td class="text-right">{{ rowData.plus24hInterne }}</td>
            <td class="text-right">{{ rowData.plus24hExterne }}</td>
            <td class="text-right">{{ rowData.moins24hInterne }}</td>
            <td class="text-right">{{ rowData.moins24hExterne }}</td>
            <td class="text-right">{{ formatNumber(rowData.sid) }}</td>
            <td class="text-right">{{ formatNumber(rowData.dei) }}</td>
            <td class="text-right">{{ formatNumber(rowData.av) }}</td>
            <td class="text-right">{{ formatNumber(rowData.ap) }}</td>
          </tr>
        </ng-template>

        <ng-template pTemplate="emptymessage">
          <tr>
            <td [attr.colspan]="cols.length" class="text-center">
              Aucune donnée disponible
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-card>
  </div>

  <!-- Graphiques -->
  <div class="col-12 lg:col-4">
    <p-card header="MAP par Train">
      <div class="chart-container">
        <p-chart
          type="bar"
          [data]="chartDataMAP"
          [options]="chartOptions"
          *ngIf="chartDataMAP">
        </p-chart>
      </div>
    </p-card>
  </div>

  <div class="col-12 lg:col-4">
    <p-card header="Autoconsommation Nette par Train">
      <div class="chart-container">
        <p-chart
          type="bar"
          [data]="chartDataAC"
          [options]="chartOptions"
          *ngIf="chartDataAC">
        </p-chart>
      </div>
    </p-card>
  </div>

  <div class="col-12 lg:col-4">
    <p-card header="Gaz Torché (Interne vs Externe)">
      <div class="chart-container">
        <p-chart
          type="bar"
          [data]="chartDataGT"
          [options]="chartOptions"
          *ngIf="chartDataGT">
        </p-chart>
      </div>
    </p-card>
  </div>
</div>
