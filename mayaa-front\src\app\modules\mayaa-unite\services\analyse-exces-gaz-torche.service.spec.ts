import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AnalyseExcesGazTorcheService } from './analyse-exces-gaz-torche.service';
import { AnalyseExcesGazTorche, AnalyseExcesGazTorcheResponse } from '../../../model/analyse-exces-gaz-torche.model';

describe('AnalyseExcesGazTorcheService', () => {
  let service: AnalyseExcesGazTorcheService;
  let httpMock: HttpTestingController;

  const mockGazTorcheData: AnalyseExcesGazTorche[] = [
    {
      codeAc: 'GT001',
      problemeSpecifique: 'Test problème gaz torché',
      intitule: 'Test gaz torché',
      ac: 425.8,
      classeCauses: 'Technique',
      causes: 'Test cause',
      actions: 'Test action',
      classes: 'Critique',
      etat: 'En cours',
      numero: '001',
      trainName: 'Train 100'
    }
  ];

  const mockResponse: AnalyseExcesGazTorcheResponse = {
    trains: mockGazTorcheData,
    totalAC: 425.8,
    totalItems: 1,
    trainsCount: 1
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [AnalyseExcesGazTorcheService]
    });
    service = TestBed.inject(AnalyseExcesGazTorcheService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getGazTorcheData', () => {
    it('should return mock data with delay', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheData(unite, mois).subscribe(data => {
        expect(data).toBeDefined();
        expect(Array.isArray(data)).toBeTrue();
        expect(data.length).toBeGreaterThan(0);
        done();
      });
    });

    it('should return data with correct structure', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheData(unite, mois).subscribe(data => {
        if (data.length > 0) {
          const item = data[0];
          expect(item.codeAc).toBeDefined();
          expect(item.problemeSpecifique).toBeDefined();
          expect(item.intitule).toBeDefined();
          expect(item.ac).toBeDefined();
          expect(item.trainName).toBeDefined();
          expect(typeof item.ac).toBe('number');
        }
        done();
      });
    });

    it('should have trains with trainName property', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheData(unite, mois).subscribe(data => {
        expect(data.every(item => item.trainName)).toBeTrue();
        expect(data.every(item => item.trainName.startsWith('Train'))).toBeTrue();
        done();
      });
    });
  });

  describe('getGazTorcheDataWithStats', () => {
    it('should return data with statistics', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheDataWithStats(unite, mois).subscribe(response => {
        expect(response).toBeDefined();
        expect(response.trains).toBeDefined();
        expect(response.totalAC).toBeDefined();
        expect(response.totalItems).toBeDefined();
        expect(response.trainsCount).toBeDefined();
        expect(typeof response.totalAC).toBe('number');
        expect(typeof response.totalItems).toBe('number');
        expect(typeof response.trainsCount).toBe('number');
        done();
      });
    });

    it('should calculate statistics correctly', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheDataWithStats(unite, mois).subscribe(response => {
        const expectedTotalAC = response.trains.reduce((sum, item) => sum + item.ac, 0);
        const uniqueTrains = [...new Set(response.trains.map(item => item.trainName))];

        expect(response.totalAC).toBe(expectedTotalAC);
        expect(response.totalItems).toBe(response.trains.length);
        expect(response.trainsCount).toBe(uniqueTrains.length);
        done();
      });
    });
  });

  describe('getGlobalStatistics', () => {
    it('should return global statistics', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGlobalStatistics(unite, mois).subscribe(stats => {
        expect(stats).toBeDefined();
        expect(stats.totalAC).toBeDefined();
        expect(stats.totalItems).toBeDefined();
        expect(stats.trainsCount).toBeDefined();
        expect(stats.statusDistribution).toBeDefined();
        expect(stats.criticalityDistribution).toBeDefined();
        expect(stats.trainStatistics).toBeDefined();
        expect(Array.isArray(stats.trainStatistics)).toBeTrue();
        done();
      });
    });

    it('should have valid distributions', (done) => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGlobalStatistics(unite, mois).subscribe(stats => {
        // Vérification que les distributions sont des objets avec des valeurs numériques
        Object.values(stats.statusDistribution).forEach(count => {
          expect(typeof count).toBe('number');
          expect(count).toBeGreaterThan(0);
        });

        Object.values(stats.criticalityDistribution).forEach(count => {
          expect(typeof count).toBe('number');
          expect(count).toBeGreaterThan(0);
        });

        // Vérification des statistiques par train
        stats.trainStatistics.forEach(trainStat => {
          expect(trainStat.trainName).toBeTruthy();
          expect(typeof trainStat.totalAC).toBe('number');
          expect(typeof trainStat.itemCount).toBe('number');
          expect(trainStat.statusDistribution).toBeDefined();
          expect(trainStat.criticalityDistribution).toBeDefined();
        });

        done();
      });
    });
  });

  describe('Mock data validation', () => {
    it('should have valid mock data structure', (done) => {
      service.getGazTorcheData('GL1K', '2024-01').subscribe(data => {
        expect(data.length).toBeGreaterThan(0);

        data.forEach(item => {
          expect(item.codeAc).toBeTruthy();
          expect(item.problemeSpecifique).toBeTruthy();
          expect(item.intitule).toBeTruthy();
          expect(item.ac).toBeGreaterThan(0);
          expect(item.classeCauses).toBeTruthy();
          expect(item.causes).toBeTruthy();
          expect(item.actions).toBeTruthy();
          expect(item.classes).toBeTruthy();
          expect(item.etat).toBeTruthy();
          expect(item.numero).toBeTruthy();
          expect(item.trainName).toBeTruthy();
        });
        done();
      });
    });

    it('should have multiple trains in mock data', (done) => {
      service.getGazTorcheData('GL1K', '2024-01').subscribe(data => {
        const uniqueTrains = [...new Set(data.map(item => item.trainName))];
        expect(uniqueTrains.length).toBeGreaterThan(1);
        done();
      });
    });

    it('should have realistic AC values', (done) => {
      service.getGazTorcheData('GL1K', '2024-01').subscribe(data => {
        data.forEach(item => {
          expect(item.ac).toBeGreaterThan(0);
          expect(item.ac).toBeLessThan(1000); // Valeur réaliste
        });
        done();
      });
    });
  });

  // Tests pour les futures intégrations API (commentés pour l'instant)
  /*
  describe('API Integration (Future)', () => {
    it('should call correct API endpoint for getGazTorcheData', () => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheData(unite, mois).subscribe();

      const req = httpMock.expectOne(`/api/mayaa-unite/analyse-exces-gaz-torche?unite=${unite}&mois=${mois}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGazTorcheData);
    });

    it('should call correct API endpoint for getGazTorcheDataWithStats', () => {
      const unite = 'GL1K';
      const mois = '2024-01';

      service.getGazTorcheDataWithStats(unite, mois).subscribe();

      const req = httpMock.expectOne(`/api/mayaa-unite/analyse-exces-gaz-torche/stats?unite=${unite}&mois=${mois}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });
  */
});
