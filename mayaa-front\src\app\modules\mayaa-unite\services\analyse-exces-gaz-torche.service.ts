import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import {
  AnalyseExcesGazTorche,
  AnalyseExcesGazTorcheResponse,
  GlobalStatistics,
  TrainStatistics
} from '../../../model/analyse-exces-gaz-torche.model';

@Injectable({
  providedIn: 'root'
})
export class AnalyseExcesGazTorcheService {

  private baseUrl = '/api/mayaa-unite'; // À ajuster selon votre configuration

  constructor(private http: HttpClient) { }

  /**
   * Récupère les données d'analyse d'excès de gaz torché pour une unité et un mois donnés
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les données d'analyse d'excès de gaz torché
   */
  getGazTorcheData(unite: string, mois: string): Observable<AnalyseExcesGazTorche[]> {
    // TODO: Remplacer par l'appel API réel quand le contrôleur sera prêt
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<AnalyseExcesGazTorche[]>(`${this.baseUrl}/analyse-exces-gaz-torche`, { params });

    // Données mock en attendant l'API
    return this.getMockGazTorcheData().pipe(delay(500));
  }

  /**
   * Récupère les données complètes avec statistiques
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les données complètes avec statistiques
   */
  getGazTorcheDataWithStats(unite: string, mois: string): Observable<AnalyseExcesGazTorcheResponse> {
    // TODO: Remplacer par l'appel API réel
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<AnalyseExcesGazTorcheResponse>(`${this.baseUrl}/analyse-exces-gaz-torche/stats`, { params });

    return this.getMockGazTorcheDataWithStats().pipe(delay(300));
  }

  /**
   * Récupère les statistiques globales
   * @param unite Code de l'unité
   * @param mois Mois au format YYYY-MM
   * @returns Observable contenant les statistiques globales
   */
  getGlobalStatistics(unite: string, mois: string): Observable<GlobalStatistics> {
    // TODO: Remplacer par l'appel API réel
    // const params = new HttpParams()
    //   .set('unite', unite)
    //   .set('mois', mois);
    // return this.http.get<GlobalStatistics>(`${this.baseUrl}/analyse-exces-gaz-torche/global-stats`, { params });

    return this.getMockGlobalStatistics().pipe(delay(200));
  }

  // Méthodes pour les données mock (à supprimer quand l'API sera prête)
  private getMockGazTorcheData(): Observable<AnalyseExcesGazTorche[]> {
    const mockData: AnalyseExcesGazTorche[] = [
      {
        codeAc: 'GT001',
        problemeSpecifique: 'Torchage excessif lors démarrage',
        intitule: 'Système torche principale',
        ac: 425.8,
        classeCauses: 'Opérationnelle',
        causes: 'Procédure démarrage non optimisée',
        actions: 'Révision procédure + formation',
        classes: 'Critique',
        etat: 'En cours',
        numero: '001',
        trainName: 'Train 100'
      },
      {
        codeAc: 'GT002',
        problemeSpecifique: 'Fuite vanne torche secours',
        intitule: 'Vanne isolation torche',
        ac: 178.3,
        classeCauses: 'Technique',
        causes: 'Usure siège vanne',
        actions: 'Remplacement vanne',
        classes: 'Majeur',
        etat: 'Planifié',
        numero: '002',
        trainName: 'Train 100'
      },
      {
        codeAc: 'GT003',
        problemeSpecifique: 'Torchage gaz purge compresseur',
        intitule: 'Système purge K-101',
        ac: 89.7,
        classeCauses: 'Maintenance',
        causes: 'Fréquence purge excessive',
        actions: 'Optimisation cycle purge',
        classes: 'Mineur',
        etat: 'Terminé',
        numero: '003',
        trainName: 'Train 100'
      },
      {
        codeAc: 'GT004',
        problemeSpecifique: 'Torchage lors arrêt urgence',
        intitule: 'Système dépressurisation',
        ac: 312.5,
        classeCauses: 'Sécurité',
        causes: 'Déclenchement intempestif',
        actions: 'Révision logique sécurité',
        classes: 'Critique',
        etat: 'En cours',
        numero: '004',
        trainName: 'Train 200'
      },
      {
        codeAc: 'GT005',
        problemeSpecifique: 'Perte efficacité brûleur torche',
        intitule: 'Brûleur torche T-201',
        ac: 156.2,
        classeCauses: 'Technique',
        causes: 'Encrassement injecteurs',
        actions: 'Nettoyage + remplacement',
        classes: 'Majeur',
        etat: 'Terminé',
        numero: '005',
        trainName: 'Train 200'
      },
      {
        codeAc: 'GT006',
        problemeSpecifique: 'Torchage gaz évent ballon',
        intitule: 'Évent ballon V-205',
        ac: 67.4,
        classeCauses: 'Opérationnelle',
        causes: 'Niveau ballon instable',
        actions: 'Réglage contrôle niveau',
        classes: 'Mineur',
        etat: 'En cours',
        numero: '006',
        trainName: 'Train 200'
      },
      {
        codeAc: 'GT007',
        problemeSpecifique: 'Torchage test sécurité',
        intitule: 'Test mensuel système',
        ac: 45.1,
        classeCauses: 'Réglementaire',
        causes: 'Test obligatoire',
        actions: 'Optimisation procédure test',
        classes: 'Mineur',
        etat: 'Planifié',
        numero: '007',
        trainName: 'Train 300'
      }
    ];

    return of(mockData);
  }

  private getMockGazTorcheDataWithStats(): Observable<AnalyseExcesGazTorcheResponse> {
    return this.getMockGazTorcheData().pipe(
      map((trains: AnalyseExcesGazTorche[]) => {
        const totalAC = trains.reduce((sum, item) => sum + item.ac, 0);
        const uniqueTrains = [...new Set(trains.map(item => item.trainName))];

        return {
          trains,
          totalAC,
          totalItems: trains.length,
          trainsCount: uniqueTrains.length
        };
      })
    );
  }

  private getMockGlobalStatistics(): Observable<GlobalStatistics> {
    return this.getMockGazTorcheData().pipe(
      map((data: AnalyseExcesGazTorche[]) => {
        const totalAC = data.reduce((sum, item) => sum + item.ac, 0);
        const uniqueTrains = [...new Set(data.map(item => item.trainName))];

        // Calcul des distributions
        const statusDistribution: { [key: string]: number } = {};
        const criticalityDistribution: { [key: string]: number } = {};

        data.forEach(item => {
          statusDistribution[item.etat] = (statusDistribution[item.etat] || 0) + 1;
          criticalityDistribution[item.classes] = (criticalityDistribution[item.classes] || 0) + 1;
        });

        // Statistiques par train
        const trainStatistics: TrainStatistics[] = uniqueTrains.map(trainName => {
          const trainData = data.filter(item => item.trainName === trainName);
          const trainTotalAC = trainData.reduce((sum, item) => sum + item.ac, 0);

          const trainStatusDist: { [key: string]: number } = {};
          const trainCriticalityDist: { [key: string]: number } = {};

          trainData.forEach(item => {
            trainStatusDist[item.etat] = (trainStatusDist[item.etat] || 0) + 1;
            trainCriticalityDist[item.classes] = (trainCriticalityDist[item.classes] || 0) + 1;
          });

          return {
            trainName,
            totalAC: trainTotalAC,
            itemCount: trainData.length,
            statusDistribution: trainStatusDist,
            criticalityDistribution: trainCriticalityDist
          };
        });

        return {
          totalAC,
          totalItems: data.length,
          trainsCount: uniqueTrains.length,
          statusDistribution,
          criticalityDistribution,
          trainStatistics
        };
      })
    );
  }
}
